<template>
  <el-aside class="layout-aside" :class="setCollapseWidth" v-if="clientWidth > 1000">
    <Logo v-if="setShowLogo && menuList.length && getThemeConfig.layout !== 'columns'" />
    <div v-if="menuList.length && getThemeConfig.layout == 'columns'" class="cat-name">
      {{ adminTitle || catName }}
    </div>
    <el-scrollbar class="flex-auto" ref="layoutAsideRef">
      <Vertical :menuList="menuList" :class="setCollapseWidth" />
    </el-scrollbar>
  </el-aside>
  <el-drawer :visible.sync="getThemeConfig.isCollapse" :with-header="false" direction="ltr" size="180px" v-else>
    <el-aside class="layout-aside w100 h100">
      <Logo v-if="setShowLogo && menuList.length" />
      <el-scrollbar class="flex-auto" ref="layoutAsideRef">
        <Vertical :menuList="menuList" />
      </el-scrollbar>
    </el-aside>
  </el-drawer>
</template>

<script>
import Vertical from '@/layout/navMenu/vertical.vue';
import Logo from '@/layout/logo/index.vue';
export default {
  name: 'layoutAside',
  components: { Vertical, Logo },
  data() {
    return {
      // menuList: [],
      clientWidth: '',
      catName: '',
    };
  },
  computed: {
    adminTitle() {
      return this.$store.state.app.adminTitle || '';
    },
    // 设置左侧菜单的具体宽度
    menuList() {
      this.$store.state.user.childMenuList.length > 0
        ? (this.$store.state.themeConfig.themeConfig.isCollapse = false)
        : (this.$store.state.themeConfig.themeConfig.isCollapse = true);
      return this.$store.state.user.childMenuList;
    },
    setCollapseWidth() {
      let { layout, isCollapse } = this.$store.state.themeConfig.themeConfig;
      let asideBrColor = '';
      layout === 'classic' || layout === 'columns' ? (asideBrColor = 'layout-el-aside-br-color') : '';

      if (layout === 'columns') {
        // 分栏布局，菜单收起时宽度给 1px / 暂为0px
        if (isCollapse) {
          return ['layout-aside-width1', asideBrColor];
        } else {
          return ['layout-aside-width-default', asideBrColor];
        }
      } else {
        // 其它布局给 64px
        if (isCollapse) {
          return ['layout-aside-width1', asideBrColor];
        } else {
          return ['layout-aside-width-default', asideBrColor, layout === 'classic' ? 'pt8' : ''];
        }
      }
    },
    // 设置 logo 是否显示
    setShowLogo() {
      let { layout, isShowLogo } = this.$store.state.themeConfig.themeConfig;
      return (isShowLogo && layout === 'defaults') || (isShowLogo && layout === 'columns');
    },
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
  },
  mounted() {
    // this.getMenus()
  },
  created() {
    this.initMenuFixed(document.body.clientWidth);
    this.setFilterRoutes();
    this.bus.$on('layoutMobileResize', (res) => {
      this.initMenuFixed(res.clientWidth);
    });
    this.bus.$on('oneCatName', (name) => {
      this.catName = name;
    });
    // 菜单滚动条监听
    this.bus.$on('updateElScrollBar', () => {
      setTimeout(() => {
        this.$refs.layoutAsideRef.update();
      }, 300);
    });
    if (this.$store.state.themeConfig.themeConfig.layout !== 'columns') {
      this.bus.$on('routesListChange', () => {
        this.setFilterRoutes();
      });
    }
  },
  beforeDestroy() {
    this.bus.$off('routesListChange');
  },
  methods: {
    getMenus() {
      this.$store.dispatch('user/getMenus');
    },
    // 设置/过滤路由（非静态路由/是否显示在菜单中）
    setFilterRoutes() {
      if (this.$store.state.themeConfig.themeConfig.layout === 'columns') return false;
      this.$store.commit('user/childMenuList', this.filterRoutesFun(this.$store.state.user.menuList));
      // this.menuList = this.filterRoutesFun(this.$store.state.user.menuList);
    },
    // 设置/过滤路由 递归函数
    filterRoutesFun(arr) {
      return arr
        .filter((item) => item.path)
        .map((item) => {
          item = Object.assign({}, item);
          if (item.children) item.children = this.filterRoutesFun(item.children);

          return item;
        });
    },
    // 设置菜单导航是否固定（移动端）
    initMenuFixed(clientWidth) {
      this.clientWidth = clientWidth;
      this.$emit('routesListChange');
    },
  },
  // 页面销毁时
  destroyed() {
    // 取消菜单滚动条监听
    this.bus.$off('updateElScrollBar', () => {});
  },
};
</script>
<style lang="scss" scoped>
.layout-aside {
  background: linear-gradient(180deg, rgba(36, 41, 51, 0.95) 0%, rgba(45, 55, 72, 0.95) 100%);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(74, 158, 255, 0.12);
  box-shadow: 4px 0 16px rgba(74, 158, 255, 0.06);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 2px;
    height: 100%;
    background: linear-gradient(180deg, #4a9eff 0%, transparent 50%, #4a9eff 100%);
    animation: sidebarGlow 4s ease-in-out infinite;
  }
}

@keyframes sidebarGlow {
  0%, 100% { opacity: 0.2; }
  50% { opacity: 0.4; }
}

.cat-name {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  font-weight: 600;
  font-size: 15px;
  color: var(--text-primary);
  background: linear-gradient(90deg, rgba(0, 212, 255, 0.1) 0%, transparent 100%);
  position: relative;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #00d4ff 50%, transparent 100%);
  }
}

// 滚动条样式
:deep(.el-scrollbar) {
  .el-scrollbar__bar {
    .el-scrollbar__thumb {
      background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
      border-radius: 4px;
      border: 1px solid rgba(0, 212, 255, 0.3);
    }
  }

  .el-scrollbar__track {
    background: rgba(0, 212, 255, 0.05);
  }
}

// 抽屉样式（移动端）
:deep(.el-drawer) {
  background: linear-gradient(180deg, rgba(26, 26, 46, 0.98) 0%, rgba(22, 33, 62, 0.98) 100%);
  backdrop-filter: blur(20px);
  border-right: 1px solid rgba(0, 212, 255, 0.3);
}

// 响应式设计
@media (max-width: 768px) {
  .cat-name {
    height: 45px;
    font-size: 14px;
  }
}
</style>
