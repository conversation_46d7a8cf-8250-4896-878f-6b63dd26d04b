// 小程序 API 接口
import request from '../utils/request.js'

// 用户认证相关
export const authApi = {
  // 登录
  login: (data) => request.post('/admin/login', data),
  
  // 获取用户信息
  getUserInfo: () => request.get('/admin/info'),
  
  // 退出登录
  logout: () => request.post('/admin/logout')
}

// 控制台相关
export const dashboardApi = {
  // 获取控制台统计数据
  getStats: () => request.get('/admin/dashboard/stats'),
  
  // 获取最近订单
  getRecentOrders: () => request.get('/admin/dashboard/recent-orders')
}

// 订单管理相关
export const orderApi = {
  // 获取订单列表
  getList: (params) => request.get('/admin/order/list', params),
  
  // 获取订单详情
  getDetail: (id) => request.get(`/admin/order/detail/${id}`),
  
  // 更新订单状态
  updateStatus: (data) => request.put('/admin/order/status', data),
  
  // 发货
  ship: (data) => request.post('/admin/order/ship', data)
}

// 用户管理相关
export const userApi = {
  // 获取用户统计
  getStats: () => request.get('/admin/user/stats'),
  
  // 获取用户列表
  getList: (params) => request.get('/admin/user/list', params),
  
  // 获取用户详情
  getDetail: (id) => request.get(`/admin/user/detail/${id}`),
  
  // 更新用户状态
  updateStatus: (data) => request.put('/admin/user/status', data)
}

// 商品管理相关
export const productApi = {
  // 获取商品列表
  getList: (params) => request.get('/admin/product/list', params),
  
  // 获取商品详情
  getDetail: (id) => request.get(`/admin/product/detail/${id}`),
  
  // 添加商品
  add: (data) => request.post('/admin/product/add', data),
  
  // 更新商品
  update: (data) => request.put('/admin/product/update', data),
  
  // 更新商品状态
  updateStatus: (data) => request.put('/admin/product/status', data),
  
  // 删除商品
  delete: (id) => request.delete(`/admin/product/delete/${id}`)
}

// 营销管理相关
export const marketingApi = {
  // 获取优惠券列表
  getCouponList: (params) => request.get('/admin/marketing/coupon/list', params),
  
  // 获取活动列表
  getActivityList: (params) => request.get('/admin/marketing/activity/list', params)
}

// 系统设置相关
export const systemApi = {
  // 获取系统配置
  getConfig: () => request.get('/admin/system/config'),
  
  // 更新系统配置
  updateConfig: (data) => request.put('/admin/system/config', data),
  
  // 文件上传
  upload: (data) => request.post('/admin/system/upload', data)
}

// 统计分析相关
export const statisticApi = {
  // 获取销售统计
  getSalesStats: (params) => request.get('/admin/statistic/sales', params),
  
  // 获取用户统计
  getUserStats: (params) => request.get('/admin/statistic/user', params),
  
  // 获取商品统计
  getProductStats: (params) => request.get('/admin/statistic/product', params)
}

// 导出所有API
export default {
  auth: authApi,
  dashboard: dashboardApi,
  order: orderApi,
  user: userApi,
  product: productApi,
  marketing: marketingApi,
  system: systemApi,
  statistic: statisticApi
}
