{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.114",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.424",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.602",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.603",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.604",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:51.608",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6c64cb25" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:58.927",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:58.927",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:37:58.928",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:00.035",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.607",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.609",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.760",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.761",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.763",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.764",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.765",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:02.766",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:19.601",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:21.211",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:21.409",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:21.437",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:28.324",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:28.339",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.d.LoggingFailureAnalysisReporter",
                    "message": "Application failed to start due to an exception" }
                    
org.springframework.boot.web.server.PortInUseException: Port 20510 is already in use
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:213)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:297)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:163)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:553)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.zbkj.front.CrmebFrontApplication.main(CrmebFrontApplication.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:109)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:38:28.347",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6c64cb25, started on Mon Jul 28 17:37:51 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:04.943",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.234",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.369",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.370",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.370",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.371",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:05.372",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@67d48005" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:12.178",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:12.179",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:12.180",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:13.340",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.489",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.490",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.603",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.604",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.606",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.607",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.608",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:15.609",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:28.806",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:30.378",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:30.512",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:30.531",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:50.716",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:50.725",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:50.792",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:50.820",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:52.212",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:52.216",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2fb7fd28]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:39:52.307",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.077",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.079",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.080",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.082",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2f4865f6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.085",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.115",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.116",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6c14d309]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.119",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.120",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.121",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.134",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.135",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@769e0520]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:21.138",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.363",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.364",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.366",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.367",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.403",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.408",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.410",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@533c2dec]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.412",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.478",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.484",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@18a3a06a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.542",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.570",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.571",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4efea9f3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.575",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.827",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0f1YcYll21YWZf4NSall2QI5FS0YcYld", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.828",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:22.928",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.278",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.280",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.292",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.294",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.330",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.332",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.363",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0f1YcYll21YWZf4NSall2QI5FS0YcYld&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.405",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.418",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.419",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@73987e53]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.423",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.525",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.526",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@20f3bb98]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.543",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.561",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.563",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.587",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.588",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@456086b4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.595",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.595",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.595",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@731780be]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:23.598",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.000",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.003",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.076",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.077",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.082",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.082",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.540",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.546",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.626",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.628",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3a46fd58]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.635",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.644",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.758",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.760",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7cfcc155]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.763",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68874588-2af0e695-2efc1d18]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.767",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.856",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.856",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.859",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@48d7641c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.859",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@960d040]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:40:24.864",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.036",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.037",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.038",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.038",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.040",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@22fda7aa]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.042",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.065",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.066",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.071",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.071",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4d791804]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.072",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.082",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.083",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6308b063]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:16.084",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.032",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.034",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.047",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.048",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.049",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.050",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@43d23983]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.055",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.056",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.057",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.057",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3c09ddbb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.058",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.059",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.180",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.181",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@358767cc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.182",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.399",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0c1Lz2Ia1N833K0jQMIa1NAJ1x0Lz2Ij", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.400",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.402",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.417",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0c1Lz2Ia1N833K0jQMIa1NAJ1x0Lz2Ij&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.418",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.690",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.691",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.701",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.702",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.705",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.705",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@782439a0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.707",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.725",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.726",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@15456bc2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.728",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.740",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.742",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.764",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.765",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2ef771be]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.765",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.817",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.818",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.829",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.861",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.862",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5c7127c4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.863",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68874725-11832364-47a95b74]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.863",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.869",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.871",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.892",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.894",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@565d0f18]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:17.894",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.130",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.132",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.161",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.162",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@334164b4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.163",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.164",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.165",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.165",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.289",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.290",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@565015fb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.291",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.325",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.326",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@362d577e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 17:47:18.327",
                    "level": "DEBUG",
                    "thread": "http-nio-20510-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.399",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.635",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.734",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.734",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.734",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.734",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:53.734",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4cc451f2" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:59.115",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:59.115",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:59.115",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:36:59.808",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.841",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.841",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.937",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.942",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.944",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.944",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.944",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:01.944",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:12.305",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:13.099",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:13.198",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:13.214",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:17.533",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:17.544",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.d.LoggingFailureAnalysisReporter",
                    "message": "Application failed to start due to an exception" }
                    
org.springframework.boot.web.server.PortInUseException: Port 20510 is already in use
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:213)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:297)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:163)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:553)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.zbkj.front.CrmebFrontApplication.main(CrmebFrontApplication.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:109)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:17.557",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@4cc451f2, started on Mon Jul 28 19:36:53 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.184",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.370",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.496",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.496",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.496",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.496",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:52.500",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@52bf72b5" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:58.161",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:58.161",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:58.169",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:37:59.017",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.640",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.642",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.766",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.766",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.769",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.769",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.769",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:00.769",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:11.666",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:12.838",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:12.934",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:12.958",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:17.114",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:17.117",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.d.LoggingFailureAnalysisReporter",
                    "message": "Application failed to start due to an exception" }
                    
org.springframework.boot.web.server.PortInUseException: Port 20510 is already in use
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:213)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:297)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:163)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:553)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.zbkj.front.CrmebFrontApplication.main(CrmebFrontApplication.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:109)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:17.130",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@52bf72b5, started on Mon Jul 28 19:37:52 CST 2025" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:41.918",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.115",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.235",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.235",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.235",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.235",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:42.235",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@59309333" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:48.003",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:48.003",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:48.003",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:48.759",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.352",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.352",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:38:51.471",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:01.549",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:02.771",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:02.863",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:02.884",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:06.925",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application failed to start with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:06.950",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.d.LoggingFailureAnalysisReporter",
                    "message": "Application failed to start due to an exception" }
                    
org.springframework.boot.web.server.PortInUseException: Port 20510 is already in use
	at org.springframework.boot.web.embedded.tomcat.TomcatWebServer.start(TomcatWebServer.java:213)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.startWebServer(ServletWebServerApplicationContext.java:297)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.finishRefresh(ServletWebServerApplicationContext.java:163)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:553)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:141)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:747)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:397)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1226)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1215)
	at com.zbkj.front.CrmebFrontApplication.main(CrmebFrontApplication.java:34)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.boot.loader.MainMethodRunner.run(MainMethodRunner.java:49)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:109)
	at org.springframework.boot.loader.Launcher.launch(Launcher.java:58)
	at org.springframework.boot.loader.JarLauncher.main(JarLauncher.java:88)
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:39:06.957",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@59309333, started on Mon Jul 28 19:38:42 CST 2025" }
                    
