<template>
  <view class="loading-state" v-if="visible">
    <!-- 全屏加载 -->
    <view v-if="type === 'fullscreen'" class="fullscreen-loading">
      <view class="loading-content">
        <view class="spinner-container">
          <view class="spinner" :class="spinnerType"></view>
        </view>
        <text class="loading-text">{{ message || '加载中...' }}</text>
        <view v-if="showProgress" class="progress-container">
          <view class="progress-bar">
            <view class="progress-fill" :style="{ width: progress + '%' }"></view>
          </view>
          <text class="progress-text">{{ progress }}%</text>
        </view>
      </view>
    </view>
    
    <!-- 内联加载 -->
    <view v-else-if="type === 'inline'" class="inline-loading">
      <view class="spinner" :class="spinnerType"></view>
      <text class="loading-text">{{ message || '加载中...' }}</text>
    </view>
    
    <!-- 按钮加载 -->
    <view v-else-if="type === 'button'" class="button-loading">
      <view class="spinner small" :class="spinnerType"></view>
      <text class="loading-text">{{ message || '处理中...' }}</text>
    </view>
    
    <!-- 骨架屏加载 -->
    <view v-else-if="type === 'skeleton'" class="skeleton-loading">
      <view class="skeleton-item" v-for="(item, index) in skeletonItems" :key="index">
        <view v-if="item.type === 'avatar'" class="skeleton-avatar"></view>
        <view v-else-if="item.type === 'title'" class="skeleton-title" :style="{ width: item.width || '60%' }"></view>
        <view v-else-if="item.type === 'text'" class="skeleton-text" :style="{ width: item.width || '80%' }"></view>
        <view v-else-if="item.type === 'image'" class="skeleton-image" :style="{ width: item.width || '100%', height: item.height || '200rpx' }"></view>
      </view>
    </view>
    
    <!-- 列表加载 -->
    <view v-else-if="type === 'list'" class="list-loading">
      <view class="spinner small" :class="spinnerType"></view>
      <text class="loading-text">{{ message || '加载更多...' }}</text>
    </view>
    
    <!-- 刷新加载 -->
    <view v-else-if="type === 'refresh'" class="refresh-loading">
      <view class="spinner small" :class="spinnerType"></view>
      <text class="loading-text">{{ message || '刷新中...' }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: 'LoadingState',
  props: {
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 加载类型：fullscreen, inline, button, skeleton, list, refresh
    type: {
      type: String,
      default: 'inline'
    },
    // 加载消息
    message: {
      type: String,
      default: ''
    },
    // 旋转器类型：default, dots, pulse, bounce
    spinnerType: {
      type: String,
      default: 'default'
    },
    // 是否显示进度
    showProgress: {
      type: Boolean,
      default: false
    },
    // 进度值 0-100
    progress: {
      type: Number,
      default: 0
    },
    // 骨架屏配置
    skeletonItems: {
      type: Array,
      default: () => [
        { type: 'title', width: '60%' },
        { type: 'text', width: '80%' },
        { type: 'text', width: '70%' }
      ]
    },
    // 自动隐藏时间（毫秒）
    autoHide: {
      type: Number,
      default: 0
    }
  },
  
  mounted() {
    if (this.autoHide > 0) {
      setTimeout(() => {
        this.$emit('hide');
      }, this.autoHide);
    }
  },
  
  methods: {
    hide() {
      this.$emit('hide');
    }
  }
};
</script>

<style lang="scss" scoped>
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 全屏加载 */
.fullscreen-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: white;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

/* 内联加载 */
.inline-loading {
  display: flex;
  align-items: center;
  gap: 20rpx;
  padding: 40rpx;
}

/* 按钮加载 */
.button-loading {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

/* 列表加载 */
.list-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 40rpx;
}

/* 刷新加载 */
.refresh-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  padding: 20rpx;
}

/* 旋转器样式 */
.spinner-container {
  margin-bottom: 20rpx;
}

.spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner.small {
  width: 40rpx;
  height: 40rpx;
  border-width: 3rpx;
}

.spinner.dots {
  width: 60rpx;
  height: 60rpx;
  background: none;
  border: none;
  position: relative;
}

.spinner.dots::before,
.spinner.dots::after {
  content: '';
  position: absolute;
  width: 12rpx;
  height: 12rpx;
  background-color: #007aff;
  border-radius: 50%;
  animation: dots 1.4s infinite ease-in-out both;
}

.spinner.dots::before {
  left: 0;
  animation-delay: -0.32s;
}

.spinner.dots::after {
  right: 0;
  animation-delay: 0s;
}

.spinner.pulse {
  background-color: #007aff;
  border: none;
  animation: pulse 1.5s ease-in-out infinite;
}

.spinner.bounce {
  background-color: #007aff;
  border: none;
  animation: bounce 2s infinite;
}

/* 加载文本 */
.loading-text {
  font-size: 28rpx;
  color: #666;
  text-align: center;
}

/* 进度条 */
.progress-container {
  margin-top: 20rpx;
  width: 200rpx;
}

.progress-bar {
  width: 100%;
  height: 8rpx;
  background-color: #f0f0f0;
  border-radius: 4rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #007aff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  margin-top: 10rpx;
}

/* 骨架屏 */
.skeleton-loading {
  padding: 40rpx;
}

.skeleton-item {
  margin-bottom: 20rpx;
}

.skeleton-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-title {
  height: 32rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-text {
  height: 24rpx;
  border-radius: 4rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-image {
  border-radius: 8rpx;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

/* 动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
