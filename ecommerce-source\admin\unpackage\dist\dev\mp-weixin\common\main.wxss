@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
/* 全局样式 */
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.app {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
/* 通用样式 */
page {
  background-color: #f8f8f9;
  font-size: 28rpx;
  color: #515a6e;
}
/* 按钮样式重置 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
  border-radius: 0;
}
button::after {
  border: none;
}
/* 输入框样式重置 */
input {
  outline: none;
  border: none;
  background: none;
}
/* 通用工具类 */
.flex {
  display: flex;
}
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.text-center {
  text-align: center;
}
.text-right {
  text-align: right;
}
.text-left {
  text-align: left;
}
.text-primary {
  color: #E93323;
}
.text-success {
  color: #19be6b;
}
.text-warning {
  color: #ff9900;
}
.text-error {
  color: #ed4014;
}
.bg-primary {
  background-color: #E93323;
}
.bg-success {
  background-color: #19be6b;
}
.bg-warning {
  background-color: #ff9900;
}
.bg-error {
  background-color: #ed4014;
}
/* 边距工具类 */
.m-0 {
  margin: 0;
}
.m-10 {
  margin: 10rpx;
}
.m-20 {
  margin: 20rpx;
}
.m-30 {
  margin: 30rpx;
}
.p-0 {
  padding: 0;
}
.p-10 {
  padding: 10rpx;
}
.p-20 {
  padding: 20rpx;
}
.p-30 {
  padding: 30rpx;
}
/* 圆角工具类 */
.radius-8 {
  border-radius: 8rpx;
}
.radius-16 {
  border-radius: 16rpx;
}
.radius-20 {
  border-radius: 20rpx;
}
.radius-circle {
  border-radius: 50%;
}
/* 阴影工具类 */
.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
