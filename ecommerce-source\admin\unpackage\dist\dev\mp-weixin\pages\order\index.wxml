<view class="order-page data-v-0ca91b30"><view class="search-bar data-v-0ca91b30"><input class="search-input data-v-0ca91b30" placeholder="搜索订单号、用户名" data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn data-v-0ca91b30" bindtap="__e">搜索</button></view><view class="filter-tabs data-v-0ca91b30"><block wx:for="{{statusTabs}}" wx:for-item="item" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['switchTab',['$0'],[[['statusTabs','value',item.value,'value']]]]]]]}}" class="{{['tab-item','data-v-0ca91b30',(currentTab===item.value)?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view><view class="order-list data-v-0ca91b30"><block wx:for="{{$root.l0}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewOrderDetail',['$0'],[[['orderList','',index]]]]]]]}}" class="order-item data-v-0ca91b30" bindtap="__e"><view class="order-header data-v-0ca91b30"><text class="order-no data-v-0ca91b30">{{order.$orig.orderNo}}</text><text class="{{['order-status','data-v-0ca91b30','status-'+order.$orig.status]}}">{{''+(order.$orig.statusText||'未知状态')+''}}</text></view><view class="order-info data-v-0ca91b30"><text class="user-name data-v-0ca91b30">{{"用户："+order.$orig.userName}}</text><text class="order-time data-v-0ca91b30">{{order.m0}}</text></view><view class="order-amount data-v-0ca91b30"><text class="amount data-v-0ca91b30">{{"¥"+order.$orig.totalAmount}}</text></view></view></block></view><block wx:if="{{loading}}"><view class="loading data-v-0ca91b30"><text class="data-v-0ca91b30">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty data-v-0ca91b30"><text class="data-v-0ca91b30">暂无订单数据</text></view></block></view>