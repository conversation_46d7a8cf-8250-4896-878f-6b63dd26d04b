@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.dashboard.data-v-65dd1eae {
  padding: 20rpx;
  background: #f8f8f9;
  min-height: 100vh;
}
.header.data-v-65dd1eae {
  background: white;
  padding: 40rpx;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
}
.header .welcome.data-v-65dd1eae {
  display: block;
  font-size: 28rpx;
  color: #999;
  margin-bottom: 10rpx;
}
.header .username.data-v-65dd1eae {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.stats-grid.data-v-65dd1eae {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-bottom: 20rpx;
}
.stat-item.data-v-65dd1eae {
  background: white;
  padding: 40rpx;
  border-radius: 16rpx;
  position: relative;
}
.stat-item .stat-value.data-v-65dd1eae {
  font-size: 48rpx;
  font-weight: bold;
  color: #E93323;
  margin-bottom: 10rpx;
}
.stat-item .stat-label.data-v-65dd1eae {
  font-size: 24rpx;
  color: #999;
}
.stat-item .stat-icon.data-v-65dd1eae {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  font-size: 40rpx;
}
.quick-actions.data-v-65dd1eae, .recent-orders.data-v-65dd1eae {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 20rpx;
}
.section-title.data-v-65dd1eae {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}
.action-grid.data-v-65dd1eae {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 20rpx;
}
.action-item.data-v-65dd1eae {
  text-align: center;
  padding: 20rpx;
}
.action-item .action-icon.data-v-65dd1eae {
  font-size: 48rpx;
  margin-bottom: 10rpx;
}
.action-item .action-label.data-v-65dd1eae {
  font-size: 24rpx;
  color: #666;
}
.order-list .order-item.data-v-65dd1eae {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}
.order-list .order-item.data-v-65dd1eae:last-child {
  border-bottom: none;
}
.order-list .order-item .order-info .order-no.data-v-65dd1eae {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
}
.order-list .order-item .order-info .order-amount.data-v-65dd1eae {
  font-size: 24rpx;
  color: #E93323;
  font-weight: bold;
}
.order-list .order-item .order-status.data-v-65dd1eae {
  font-size: 24rpx;
  color: #19be6b;
  padding: 10rpx 20rpx;
  background: rgba(25, 190, 107, 0.1);
  border-radius: 20rpx;
}
