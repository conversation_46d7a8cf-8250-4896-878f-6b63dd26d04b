<template>
  <div class="tech-layout-container">
    <!-- 科技感背景动画 -->
    <div class="tech-background">
      <div class="tech-grid"></div>
      <div class="tech-particles"></div>
    </div>

    <el-container class="layout-container tech-glass-container">
      <Asides class="tech-sidebar" />
      <el-container class="flex-center layout-backtop">
        <Headers v-if="isFixedHeader" class="tech-header" />
        <el-scrollbar ref="layoutDefaultsScrollbarRef" class="tech-scrollbar">
          <Headers v-if="!isFixedHeader" class="tech-header" />
          <Mains class="tech-main" />
        </el-scrollbar>
      </el-container>
      <el-backtop target=".layout-backtop .el-scrollbar__wrap" class="tech-backtop"></el-backtop>
    </el-container>
  </div>
</template>

<script>
import Asides from '@/layout/component/aside.vue';
import Headers from '@/layout/component/header.vue';
import Mains from '@/layout/component/main.vue';
export default {
  name: 'layoutDefaults',
  components: { Asides, Headers, Mains },
  data() {
    return {};
  },
  computed: {
    // 是否开启固定 header
    isFixedHeader() {
      return this.$store.state.themeConfig.themeConfig.isFixedHeader;
    },
  },
  watch: {
    // 监听路由的变化
    $route: {
      handler() {
        this.$refs.layoutDefaultsScrollbarRef.wrap.scrollTop = 0;
      },
      deep: true,
    },
  },
};
</script>

<style lang="scss" scoped>
.tech-layout-container {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.tech-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  background: linear-gradient(135deg, #1a1d23 0%, #242933 50%, #2d3748 100%);
}

.tech-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    linear-gradient(rgba(74, 158, 255, 0.04) 1px, transparent 1px),
    linear-gradient(90deg, rgba(74, 158, 255, 0.04) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 30s linear infinite;
}

.tech-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 20% 80%, rgba(74, 158, 255, 0.05) 0%, transparent 60%),
              radial-gradient(circle at 80% 20%, rgba(74, 158, 255, 0.05) 0%, transparent 60%),
              radial-gradient(circle at 40% 40%, rgba(74, 158, 255, 0.03) 0%, transparent 60%);
  animation: particleFloat 20s ease-in-out infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes particleFloat {
  0%, 100% { opacity: 0.3; transform: translateY(0px); }
  50% { opacity: 0.6; transform: translateY(-20px); }
}

.tech-glass-container {
  background: rgba(26, 26, 46, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(0, 212, 255, 0.1);
  height: 100vh;
}

.tech-sidebar {
  background: linear-gradient(180deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 4px 0 20px rgba(0, 212, 255, 0.1);
}

.tech-header {
  background: linear-gradient(90deg, rgba(26, 26, 46, 0.95) 0%, rgba(22, 33, 62, 0.95) 100%);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(0, 212, 255, 0.2);
  box-shadow: 0 2px 20px rgba(0, 212, 255, 0.1);
}

.tech-main {
  background: rgba(12, 12, 12, 0.3);
  backdrop-filter: blur(5px);
  min-height: calc(100vh - 60px);
  padding: 20px;
}

.tech-scrollbar {
  :deep(.el-scrollbar__bar) {
    .el-scrollbar__thumb {
      background: linear-gradient(180deg, #00d4ff 0%, #0099cc 100%);
      border-radius: 4px;
    }
  }

  :deep(.el-scrollbar__track) {
    background: rgba(0, 212, 255, 0.1);
  }
}

.tech-backtop {
  :deep(.el-backtop) {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    border: 1px solid rgba(0, 212, 255, 0.3);
    box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 30px rgba(0, 212, 255, 0.5);
    }

    i {
      color: #ffffff;
      font-weight: bold;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tech-main {
    padding: 15px;
  }

  .tech-grid {
    background-size: 30px 30px;
  }
}
</style>
