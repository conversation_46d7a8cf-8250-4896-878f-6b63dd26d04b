@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.order-page.data-v-0ca91b30 {
  background: #f8f8f9;
  min-height: 100vh;
}
.search-bar.data-v-0ca91b30 {
  display: flex;
  padding: 20rpx;
  background: white;
}
.search-bar .search-input.data-v-0ca91b30 {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-bar .search-btn.data-v-0ca91b30 {
  width: 120rpx;
  height: 70rpx;
  background: #E93323;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.filter-tabs.data-v-0ca91b30 {
  display: flex;
  background: white;
  border-top: 1rpx solid #f0f0f0;
}
.filter-tabs .tab-item.data-v-0ca91b30 {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}
.filter-tabs .tab-item.active.data-v-0ca91b30 {
  color: #E93323;
  border-bottom-color: #E93323;
}
.order-list.data-v-0ca91b30 {
  padding: 20rpx;
}
.order-item.data-v-0ca91b30 {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.order-item .order-header.data-v-0ca91b30 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}
.order-item .order-header .order-no.data-v-0ca91b30 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}
.order-item .order-header .order-status.data-v-0ca91b30 {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}
.order-item .order-header .order-status.status-unpaid.data-v-0ca91b30 {
  background: rgba(255, 153, 0, 0.1);
  color: #ff9900;
}
.order-item .order-header .order-status.status-unshipped.data-v-0ca91b30 {
  background: rgba(45, 183, 245, 0.1);
  color: #2db7f5;
}
.order-item .order-header .order-status.status-completed.data-v-0ca91b30 {
  background: rgba(25, 190, 107, 0.1);
  color: #19be6b;
}
.order-item .order-info.data-v-0ca91b30 {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.order-item .order-info .user-name.data-v-0ca91b30, .order-item .order-info .order-time.data-v-0ca91b30 {
  font-size: 26rpx;
  color: #999;
}
.order-item .order-amount.data-v-0ca91b30 {
  text-align: right;
}
.order-item .order-amount .amount.data-v-0ca91b30 {
  font-size: 36rpx;
  font-weight: bold;
  color: #E93323;
}
.loading.data-v-0ca91b30, .empty.data-v-0ca91b30 {
  text-align: center;
  padding: 80rpx;
  color: #999;
}
