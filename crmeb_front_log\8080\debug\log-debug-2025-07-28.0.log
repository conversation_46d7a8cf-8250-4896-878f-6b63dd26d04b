{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:18.763",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.l.ClasspathLoggingApplicationListener",
                    "message": "Application started with classpath: [jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-service-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/crmeb-common-0.0.1-SNAPSHOT.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javax.servlet-api-4.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jstl-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-jasper-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-core-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-annotations-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-el-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ecj-3.18.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-jsp-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-el-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-servlet-api-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/fastjson-1.2.83.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/druid-1.1.20.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mysql-connector-j-8.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/protobuf-java-3.21.9.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lombok-1.18.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-boot-starter-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-jdbc-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HikariCP-3.4.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jdbc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-test-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-test-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-core-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-jcl-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-extension-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-core-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-annotation-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-3.5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-plus-generator-3.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-annotations-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger2-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spi-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-core-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/byte-buddy-1.10.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-schema-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-swagger-common-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/springfox-spring-web-2.9.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/guava-20.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/classmate-1.5.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-core-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-plugin-metadata-1.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mapstruct-1.2.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-models-1.5.21.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-annotations-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/swagger-bootstrap-ui-1.9.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-classic-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logback-core-1.2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/logstash-logback-encoder-5.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-databind-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-core-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-web-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-logging-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-to-slf4j-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/log4j-api-2.12.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jul-to-slf4j-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.annotation-api-1.3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/snakeyaml-1.25.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-json-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jdk8-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-datatype-jsr310-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-module-parameter-names-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-tomcat-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/tomcat-embed-websocket-9.0.33.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-validation-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jakarta.validation-api-2.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hibernate-validator-6.0.18.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jboss-logging-3.4.1.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-web-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-webmvc-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-expression-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-data-redis-2.2.0.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-redis-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-keyvalue-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-tx-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-oxm-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-context-support-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/lettuce-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-common-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-handler-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-resolver-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-buffer-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-codec-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/netty-transport-4.1.48.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactor-core-3.3.4.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/reactive-streams-1.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jedis-3.1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-pool2-2.7.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-starter-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-starter-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mybatis-spring-boot-autoconfigure-1.3.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-spring-boot-autoconfigure-1.2.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/pagehelper-5.1.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jsqlparser-1.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/validation-api-1.1.0.Final.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-data-commons-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-beans-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/hutool-all-4.5.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-autoconfigure-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-actuator-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/micrometer-core-1.3.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/HdrHistogram-2.1.11.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/LatencyUtils-2.0.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpclient-4.5.6.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpcore-4.4.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-logging-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-codec-1.13.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-aop-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-aop-5.2.5.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aspectjweaver-1.9.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-lang3-3.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-collections4-4.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/poi-ooxml-schemas-3.17.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlbeans-2.6.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/curvesapi-1.04.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-fileupload-1.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/commons-io-2.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/thumbnailator-0.4.8.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-sdk-oss-3.5.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jdom-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jettison-1.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/stax-api-1.0.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-core-3.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ram-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-sts-3.0.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aliyun-java-sdk-ecs-4.2.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/cos_api-5.6.22.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/joda-time-2.10.5.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/bcprov-jdk15on-1.64.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/qiniu-java-sdk-7.2.28.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okhttp-3.14.7.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/okio-1.17.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-s3-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-kms-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/aws-java-sdk-core-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/ion-java-1.0.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jackson-dataformat-cbor-2.10.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jmespath-java-1.12.99.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/dom4j-1.6.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xml-apis-1.0.b2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xstream-1.4.18.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/mxparser-1.2.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/xmlpull-1.1.3.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/android-json-0.0.20131108.vaadin1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/httpmime-4.5.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/core-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/javase-3.3.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jcommander-1.72.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jai-imageio-core-1.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jjwt-0.9.1.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/jwks-rsa-0.9.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/alipay-sdk-java-4.15.20.ALL.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-security-2.2.6.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-config-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-core-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-security-web-5.2.2.RELEASE.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/spring-boot-starter-captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/captcha-1.3.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-path-2.4.0.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/json-smart-2.3.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/accessors-smart-1.2.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/asm-5.0.4.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/slf4j-api-1.7.30.jar!/, jar:file:/E:/解析包/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/lib/gson-2.8.6.jar!/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:18.954",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.boot.SpringApplication",
                    "message": "Loading source class com.zbkj.front.CrmebFrontApplication" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:19.077",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Activated activeProfiles dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:19.077",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application.yml' (classpath:/application.yml)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:19.077",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Profiles already activated, '[dev]' will not be applied" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:19.077",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.c.c.ConfigFileApplicationListener",
                    "message": "Loaded config file 'jar:file:/E:/%E8%A7%A3%E6%9E%90%E5%8C%85/ecommerce-source/crmeb/crmeb-front/target/Crmeb-front.jar!/BOOT-INF/classes!/application-dev.yml' (classpath:/application-dev.yml) for profile dev" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:19.082",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext",
                    "message": "Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6572421" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:24.894",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:24.894",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "Code archive: E:\解析包\ecommerce-source\crmeb\crmeb-front\target\Crmeb-front.jar" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:24.894",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.e.t.TomcatServletWebServerFactory",
                    "message": "None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored." }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:25.619",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.web.context.ContextLoader",
                    "message": "Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.421",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping filters: filterRegistrationBean urls=[/*] order=-2147483647, springSecurityFilterChain urls=[/*] order=-100, filterRegistrationBean urls=[/*] order=2147483647, filterRegistrationBean urls=[/*] order=2147483647, characterEncodingFilter urls=[/*] order=-2147483648, formContentFilter urls=[/*] order=-9900, requestContextFilter urls=[/*] order=-105, corsFilter urls=[/*] order=2147483647" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.421",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.ServletContextInitializerBeans",
                    "message": "Mapping servlets: statViewServlet urls=[/druid/*], dispatcherServlet urls=[/]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.547",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.a.m.w.servlet.WebMvcMetricsFilter",
                    "message": "Filter 'webMvcMetricsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.547",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedRequestContextFilter",
                    "message": "Filter 'requestContextFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.547",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.springframework.web.filter.CorsFilter",
                    "message": "Filter 'corsFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.547",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedCharacterEncodingFilter",
                    "message": "Filter 'characterEncodingFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.547",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.DelegatingFilterProxyRegistrationBean$1",
                    "message": "Filter 'springSecurityFilterChain' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:27.560",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.b.w.s.f.OrderedFormContentFilter",
                    "message": "Filter 'formContentFilter' configured for use" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:38.964",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "163 mappings in 'requestMappingHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:39.783",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerAdapter",
                    "message": "ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:39.893",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Patterns [/webjars/**, /**, /doc.html] in 'resourceHandlerMapping'" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:40:39.909",
                    "level": "DEBUG",
                    "thread": "main",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "ControllerAdvice beans: 1 @ExceptionHandler, 2 ResponseBodyAdvice" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.022",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Detected StandardServletMultipartResolver" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.043",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.223",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.282",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Mapped to ResourceHttpRequestHandler ["classpath:/static/"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.298",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.r.ResourceHttpRequestHandler",
                    "message": "Resource not found" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.304",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 404 NOT_FOUND" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.336",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": ""ERROR" dispatch for GET "/error", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.347",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#errorHtml(HttpServletRequest, HttpServletResponse)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.486",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.v.ContentNegotiatingViewResolver",
                    "message": "Selected 'text/html' given [text/html, text/html;q=0.8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.500",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Exiting from "ERROR" dispatch, status 404" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.702",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/favicon.ico", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.710",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.handler.SimpleUrlHandlerMapping",
                    "message": "Mapped to ResourceHttpRequestHandler ["classpath:/static/"]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.710",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.r.ResourceHttpRequestHandler",
                    "message": "Resource not found" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.710",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 404 NOT_FOUND" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.710",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": ""ERROR" dispatch for GET "/error", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.710",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController#error(HttpServletRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.804",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.HttpEntityMethodProcessor",
                    "message": "Using 'application/json;q=0.8', given [image/avif, image/webp, image/apng, image/svg+xml, image/*, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.804",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.HttpEntityMethodProcessor",
                    "message": "Writing [{timestamp=Mon Jul 28 19:41:46 CST 2025, status=404, error=Not Found, message=No message available,  (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 19:41:46.874",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Exiting from "ERROR" dispatch, status 404" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.124",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.129",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.139",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.139",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.161",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.161",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.213",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.213",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5722ef15]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.217",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.661",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.661",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1d06af85]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.665",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.678",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@507b4ef0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:36.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.261",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.263",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.264",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.265",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@195154e8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.265",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.275",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.275",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.276",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.280",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.472",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.472",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4e4d8e3d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.487",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.562",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0c1KfC0w3Kt2l535KV2w3zB1sq3KfC0u", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.565",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.585",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.587",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.624",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.626",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@21c03f89]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.632",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.743",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.743",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7ea7231d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.743",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.785",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.785",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.791",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.794",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.794",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.904",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.905",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@795634b3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.907",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.915",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:40.916",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.020",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.020",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@13da472b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.029",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.046",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.053",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@52a5e616]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.053",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.247",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.255",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.296",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.296",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.304",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.306",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.522",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0c1KfC0w3Kt2l535KV2w3zB1sq3KfC0u&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.552",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.700",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.701",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1b3f71a4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.709",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7fcc5384]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@389381bf]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.878",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:41.878",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.519",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.527",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.559",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.593",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.596",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7251fb54]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.598",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68876b91-141d3ec8-1d1bed2b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:42.598",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.597",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/206", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.597",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.611",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.611",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3f2f9169]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.616",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.696",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.697",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.704",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.704",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4039b2ed]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.706",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.718",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.718",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2dba57a0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.720",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.720",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.720",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.735",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.735",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@518d6f5e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.735",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.889",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.892",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.919",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.926",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7c182a62]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.930",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.942",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.942",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.946",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:49.948",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.035",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.035",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@52dc9e9d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.035",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.044",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.044",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@35f704fa]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:50.045",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.769",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.771",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.785",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.787",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@11d96468]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.794",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.876",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.882",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.882",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2a149e15]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.884",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.884",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.887",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.907",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.908",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@74f941e4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.908",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.914",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.914",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.925",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.925",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3a08293c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:53.925",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.069",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.069",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.088",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.088",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@43cd3235]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.090",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.111",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.113",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.113",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.115",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.187",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.187",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@169055a5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.187",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.215",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.215",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1e8b5cfc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:54.216",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.228",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/category", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.228",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getCategory()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.310",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.310",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@74232589]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.313",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.371",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.373",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.385",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.385",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@27971de]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:22:58.388",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@634012fc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.004",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.081",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.086",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@11a6d55f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.249",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.249",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@170d8008]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:04.249",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:11.798",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:11.800",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:11.801",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:11.801",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1796b9b2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:11.801",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.691",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4452ad0a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.692",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.703",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.724",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.725",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7e005ee4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.727",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.745",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.747",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@36a9f945]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.748",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.759",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.761",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6de1013e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.763",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.765",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.796",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.796",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.809",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.809",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7096ad4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:13.809",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:20.225",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:20.233",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:20.235",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:20.236",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@f235e8f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:23:20.239",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.689",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.689",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.689",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.689",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.693",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.693",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@619e7b98]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.693",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.717",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.717",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@69528a04]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.717",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.751",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.755",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.759",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.759",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3e7b73c8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:46.759",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.874",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.875",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.877",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.877",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4f88ce5e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.877",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.895",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.911",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.911",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@8d0c756]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.916",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.983",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.984",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1a86b241]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:47.984",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.167",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0b1BnXml21U53g40R7ol2Y9z4M3BnXmu", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.167",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.167",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.167",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.171",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.180",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0b1BnXml21U53g40R7ol2Y9z4M3BnXmu&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.180",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.185",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.185",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@15b28d37]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.185",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.288",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.288",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.295",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.297",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2ba9aa6b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.297",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.308",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.308",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.325",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.329",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2eea4cf2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.329",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.335",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.337",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.347",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.347",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@d226620]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.349",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.554",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.554",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.567",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@42646d4e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68876c0f-02786716-7762978f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.779",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.781",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.795",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.795",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4885979]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.795",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.949",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.949",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1cbdd146]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.951",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.979",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.979",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@ba4c733]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 20:24:48.983",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:48.995",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:48.997",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:48.997",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:48.998",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:48.999",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.000",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6ce902b9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.036",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.037",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6373a14]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.037",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.084",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.085",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.091",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.092",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4b1b0248]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.092",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.992",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.993",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.994",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.994",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.994",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.995",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.995",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.996",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@35d620e8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:49.997",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.006",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.007",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@454e5ba0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.015",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.104",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.104",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@13d5ba3c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.105",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.367",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.367",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0d12QvFa1Id91K0NNwIa1PG74i22QvFM", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.368",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.368",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.369",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.382",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0d12QvFa1Id91K0NNwIa1PG74i22QvFM&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.383",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.385",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.386",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1e4c7f34]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.387",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.419",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.420",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.427",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.431",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.431",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@44a4f49e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.432",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.443",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.444",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.448",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.449",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@12e22618]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.450",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.459",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.460",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@757e599c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.460",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.713",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.714",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.739",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.739",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@8bdf12]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.740",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.753",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.754",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.760",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.762",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.762",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.763",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.764",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.805",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.806",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6df8149c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.807",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68877c01-18ea955a-0c6e5410]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.807",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.862",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.863",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@68ef749d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.864",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.881",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.882",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1739d532]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:32:50.883",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.030",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.031",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.044",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.046",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4e6c041f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.053",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.112",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.113",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.114",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.115",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.117",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.118",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@757e3952]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.129",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.129",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1911c4c1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.130",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.134",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.148",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.149",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@639c07a5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.158",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.216",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.217",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.247",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.248",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@20499f3d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.249",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.249",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.250",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.250",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.252",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.332",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.332",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@33a85f85]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.333",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.365",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.366",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@29549cfc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:02.367",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.207",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/category", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.208",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getCategory()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.237",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.238",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@371b6867]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.239",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.298",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.299",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.316",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.317",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@380c7ce9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:04.319",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.316",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.317",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.319",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.319",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@59e651fd]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.320",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.396",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.397",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.409",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.409",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.413",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.414",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@45132314]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.415",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.515",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.516",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1bf7ea42]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:12.517",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.528",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.528",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.530",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.530",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.531",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.532",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@18d08201]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.533",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.538",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.539",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.539",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.540",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.546",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.551",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.551",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@602ef1d8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.552",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.561",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.562",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@71d35c52]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.563",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.586",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.586",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@72cf8763]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.587",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.621",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.623",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.639",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.640",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@60c20508]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:17.641",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:28.644",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0b1A1lGa1t6k0K0P6MHa1ry3k54A1lGE", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:28.644",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:28.647",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=微信用户, sex=null, province=null, city=null, country=null, avatar=, s (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:28.656",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0b1A1lGa1t6k0K0P6MHa1ry3k54A1lGE&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:28.657",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.028",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.028",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.043",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.076",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.076",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1fd4dc50]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.077",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68877c28-29adfbe3-5f98f0db]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:33:29.077",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.796",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.796",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.797",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.797",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.798",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.799",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@71238143]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.799",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.818",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.818",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6f750bb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.819",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.885",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.887",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.893",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.894",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@36844cc6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:07.894",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.848",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.849",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.849",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.849",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.850",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.850",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.850",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.851",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4b0aab94]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.852",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.858",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.858",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@68e8978d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.863",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.932",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.933",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@630b21b3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:08.933",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.238",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0c1gR32w3HzQj53oY94w3biAgs2gR32H", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.239",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.240",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.246",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0c1gR32w3HzQj53oY94w3biAgs2gR32H&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.247",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.251",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.253",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.271",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.271",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@666d4612]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.272",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.365",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.366",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.370",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.371",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.374",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.375",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6a021b75]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.376",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.381",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.382",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.386",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.386",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@69564b3e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.387",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.390",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.390",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@67e25c7f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.391",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.629",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.630",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.641",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.675",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.675",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@55ff8089]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.676",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68877c50-467eaed1-63c4219d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.676",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.747",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.748",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.765",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.766",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2d60860b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.767",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.799",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.799",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.801",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.801",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.881",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.881",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@528776e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.882",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.896",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.897",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6ddf19f8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:34:09.898",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.884",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.884",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.885",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.885",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.886",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.887",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@362d577e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.887",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.906",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.907",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@68ee5ee1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.908",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.934",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.935",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.941",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.942",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@70cf6bae]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:01.942",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.823",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.823",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.824",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.824",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.824",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.824",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.826",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.826",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@68937eb5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.828",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.834",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.835",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6f70f5cf]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.840",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.910",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.910",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@48c1094b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:02.911",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.122",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0e1Am90w3R6Ll53LoU2w3FESB23Am906", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.124",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.125",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.134",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0e1Am90w3R6Ll53LoU2w3FESB23Am906&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.135",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.140",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.141",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3965fb33]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.142",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.152",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.153",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.159",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.160",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@798c2a23]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.161",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.161",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.163",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.176",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.177",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.179",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.179",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1f0872fa]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.180",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.186",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.187",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@28edf05e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.188",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.534",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.535",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.541",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.542",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.554",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.554",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.555",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6c6cfa6f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.555",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.571",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.573",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.590",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.591",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@16e873c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.591",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68877f92-4298ccaa-369f33ab]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.592",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.596",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.598",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.669",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.670",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2090f3c9]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.671",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.688",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.689",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@508b5ad1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:48:03.690",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.417",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.418",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.418",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.419",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.420",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.420",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1c77bfbe]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.421",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.448",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.449",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6c414655]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.449",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-13",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.464",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.465",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.470",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.471",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@719b1c62]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:43.471",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.296",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.297",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.299",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.300",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5da80fa6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.300",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.301",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.302",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.302",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.302",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.308",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.309",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1a3e69a0]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.314",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.392",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.393",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@19e9d724]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.394",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.606",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.606",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0f15I90w3kjEl53bn20w3K3b6O05I90f", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.607",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.607",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.608",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.616",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0f15I90w3kjEl53bn20w3K3b6O05I90f&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.616",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.622",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.622",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6fe23afb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.623",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.645",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.646",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.652",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.653",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.653",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6668d288]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.654",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.654",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.672",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.673",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.674",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.675",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@38ed7202]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.675",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.684",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@32b96a6d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:44.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.023",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.023",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.023",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.025",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.034",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.044",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.045",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7784457e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.046",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.062",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.063",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4b3092a6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.064",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 688780e8-28602a0b-7fcf59bf]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.064",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.070",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.072",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.072",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.073",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.156",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.157",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@40ab19fa]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.159",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.173",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.173",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5abefafc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 21:53:45.175",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.526",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.526",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.527",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.527",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.528",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.529",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@453f01e3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.529",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.559",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.560",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7eb330fe]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.561",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.576",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.577",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.582",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.582",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@625a4dcb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:28.583",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.467",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.468",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.469",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.470",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3f50bd5d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.471",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.472",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.473",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.474",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.474",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.482",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.484",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5c934ab8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.491",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.566",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.566",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1c23dc1b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.567",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.727",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.727",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0f12GRkl2sdZ0g4Zwaml2fMcl542GRkc", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.728",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.728",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.729",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.739",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0f12GRkl2sdZ0g4Zwaml2fMcl542GRkc&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.739",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.744",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.744",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7a7c63b7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.745",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.839",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.842",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.844",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.850",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.851",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@38817ef3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.851",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.861",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.861",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.862",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@683708bc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.862",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.863",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.866",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.866",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@26bc1c9e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:29.867",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.115",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.115",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.130",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.157",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.157",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@79a2d903]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.158",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 688785c5-76125c7a-70713711]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.158",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.256",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.257",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.273",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.274",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@77d1bdfb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.274",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-15",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.290",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.290",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.291",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.291",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.362",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.363",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3965fa29]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.364",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.380",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.380",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3b5f26f7]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:14:30.381",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.302",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/login/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.303",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.303",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.303",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#getLoginConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.304",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.304",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@65679be6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.305",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.326",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.326",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6a33e520]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.330",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.382",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index/color/config", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.383",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getColorConfig()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.388",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.388",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6e493c8a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:54.388",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.261",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/pagediy/info/0", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.261",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.261",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.263",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.PageDiyController#info(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.268",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.270",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.271",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.272",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2489a13a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.272",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.273",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.274",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1caf67b2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.278",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.343",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.344",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@399f3005]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.344",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.577",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login?code=0f1jNjFa1ru51K0EPmFa1no9t64jNjFn", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.578",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.578",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.579",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.579",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [RegisterThirdUserRequest(nickName=null, sex=null, province=null, city=null, country=null, avatar=nul (truncated)...]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.587",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/sns/jscode2session?appid=111111&secret=111111&js_code=0f1jNjFa1ru51K0EPmFa1no9t64jNjFn&grant_type=authorization_code" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.595",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.595",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@41db46aa]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.596",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.612",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/bargain/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.613",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.BargainController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.618",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/combination/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.619",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.619",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@60cdf12a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.619",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CombinationController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.620",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.631",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/seckill/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.632",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.SecKillController#index()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.633",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.633",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@29ca4228]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.634",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.636",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.636",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1ade103d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.637",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.966",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=5", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.967",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.969",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.970",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.979",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.985",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.985",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@73faaea2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:55.986",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.007",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.007",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@18cad0c6]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.008",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid, rid: 68878f3f-3a5179f5-282dd255]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.008",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.008",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/88,89,87", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.009",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/byids/90,91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.009",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.011",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductByIds(String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.076",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.077",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@34ff607b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.078",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.117",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.117",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@49fdc7cc]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:54:56.118",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.091",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/category", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.093",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getCategory()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.123",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@137dd6a3]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.125",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.179",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.180",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.197",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.198",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6f7dcdab]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:27.199",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.098",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.100",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.101",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.102",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@50d3450e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.103",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.153",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.154",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.166",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.167",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.169",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.170",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1267f246]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.172",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.270",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.270",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6bad07d2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:32.272",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.503",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/token/is/exist", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.504",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.505",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.505",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.505",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.LoginController#tokenIsExist()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.506",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.506",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.506",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.507",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.508",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7b8a5ae4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.509",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.516",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.517",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@77bfcaa8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.518",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.524",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.531",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.533",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5aeebfba]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.535",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.557",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.558",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@26a22e63]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.559",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.559",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.582",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/get/bottom/navigation", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.582",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getBottomNavigation()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.597",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.597",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@26872c0d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-28 22:55:36.598",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-5",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
