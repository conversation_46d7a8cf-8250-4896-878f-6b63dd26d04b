@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.container.data-v-57280228 {
  padding: 40rpx;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
.header.data-v-57280228 {
  text-align: center;
  margin-bottom: 60rpx;
}
.header .title.data-v-57280228 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}
.header .subtitle.data-v-57280228 {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}
.content.data-v-57280228 {
  margin-bottom: 60rpx;
}
.card.data-v-57280228 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}
.card .card-title.data-v-57280228 {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.card .card-desc.data-v-57280228 {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.card .card-desc.data-v-57280228:last-child {
  margin-bottom: 0;
}
.navigation.data-v-57280228 {
  margin-bottom: 40rpx;
}
.nav-title.data-v-57280228 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-align: center;
  margin-bottom: 40rpx;
}
.nav-grid.data-v-57280228 {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20rpx;
}
.nav-item.data-v-57280228 {
  background: white;
  border-radius: 16rpx;
  padding: 40rpx 20rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
  transition: -webkit-transform 0.2s;
  transition: transform 0.2s;
  transition: transform 0.2s, -webkit-transform 0.2s;
}
.nav-item.data-v-57280228:active {
  -webkit-transform: scale(0.95);
          transform: scale(0.95);
}
.nav-icon.data-v-57280228 {
  font-size: 48rpx;
  margin-bottom: 16rpx;
}
.nav-text.data-v-57280228 {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
}
