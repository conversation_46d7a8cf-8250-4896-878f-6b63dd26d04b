{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?3f71", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?89c9", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?fb75", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?164a", "uni-app:///pages/dashboard/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?e621", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/dashboard/index.vue?63db"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userInfo", "statsData", "label", "value", "icon", "quickActions", "action", "recentOrders", "onLoad", "methods", "getUserInfo", "loadDashboardData", "setTimeout", "todayOrders", "todaySales", "totalUsers", "totalProducts", "orderNo", "amount", "status", "console", "updateStatsData", "handleAction", "uni", "url", "title"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkD77B;EACAC;IACA;MACAC;MACAC,YACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,EACA;MACAC,eACA;QAAAH;QAAAE;QAAAE;MAAA,GACA;QAAAJ;QAAAE;QAAAE;MAAA,GACA;QAAAJ;QAAAE;QAAAE;MAAA,GACA;QAAAJ;QAAAE;QAAAE;MAAA,EACA;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACAC;MACA;MACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;oBACA;sBACAC;sBACAC;sBACAC;sBACAC;oBACA;;oBAEA;oBACA,sBACA;sBAAAC;sBAAAC;sBAAAC;oBAAA,GACA;sBAAAF;sBAAAC;sBAAAC;oBAAA,GACA;sBAAAF;sBAAAC;sBAAAC;oBAAA,EACA;kBACA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA;UACAC;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;QACA;UACAD;YAAAC;UAAA;UACA;QACA;UACAD;YACAE;YACArB;UACA;UACA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrIA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/dashboard/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/dashboard/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=65dd1eae&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=65dd1eae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"65dd1eae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/dashboard/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=65dd1eae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"dashboard\">\n    <view class=\"header\">\n      <text class=\"welcome\">欢迎回来</text>\n      <text class=\"username\">{{ userInfo.realName || '管理员' }}</text>\n    </view>\n    \n    <view class=\"stats-grid\">\n      <view class=\"stat-item\" v-for=\"(item, index) in statsData\" :key=\"index\">\n        <view class=\"stat-value\">{{ item.value }}</view>\n        <view class=\"stat-label\">{{ item.label }}</view>\n        <view class=\"stat-icon\">{{ item.icon }}</view>\n      </view>\n    </view>\n    \n    <view class=\"quick-actions\">\n      <view class=\"section-title\">快捷操作</view>\n      <view class=\"action-grid\">\n        <view \n          class=\"action-item\" \n          v-for=\"(action, index) in quickActions\" \n          :key=\"index\"\n          @click=\"handleAction(action)\"\n        >\n          <view class=\"action-icon\">{{ action.icon }}</view>\n          <text class=\"action-label\">{{ action.label }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"recent-orders\">\n      <view class=\"section-title\">最近订单</view>\n      <view class=\"order-list\">\n        <view \n          class=\"order-item\" \n          v-for=\"(order, index) in recentOrders\" \n          :key=\"index\"\n        >\n          <view class=\"order-info\">\n            <text class=\"order-no\">订单号：{{ order.orderNo }}</text>\n            <text class=\"order-amount\">¥{{ order.amount }}</text>\n          </view>\n          <view class=\"order-status\">{{ order.status }}</view>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      userInfo: {},\n      statsData: [\n        { label: '今日订单', value: '0', icon: '📋' },\n        { label: '今日销售额', value: '¥0', icon: '💰' },\n        { label: '用户总数', value: '0', icon: '👥' },\n        { label: '商品总数', value: '0', icon: '📦' }\n      ],\n      quickActions: [\n        { label: '订单管理', icon: '📋', action: 'orders' },\n        { label: '商品管理', icon: '📦', action: 'products' },\n        { label: '用户管理', icon: '👥', action: 'users' },\n        { label: '营销活动', icon: '🎯', action: 'marketing' }\n      ],\n      recentOrders: []\n    }\n  },\n  \n  onLoad() {\n    this.getUserInfo()\n    this.loadDashboardData()\n  },\n  \n  methods: {\n    getUserInfo() {\n      const userInfo = uni.getStorageSync('userInfo')\n      if (userInfo) {\n        this.userInfo = userInfo\n      }\n    },\n    \n    async loadDashboardData() {\n      try {\n        // 模拟加载统计数据\n        setTimeout(() => {\n          this.updateStatsData({\n            todayOrders: '12',\n            todaySales: '3580.00',\n            totalUsers: '1,234',\n            totalProducts: '89'\n          })\n\n          // 模拟最近订单数据\n          this.recentOrders = [\n            { orderNo: 'ORD20250128001', amount: '299.00', status: '已完成' },\n            { orderNo: 'ORD20250128002', amount: '158.50', status: '待发货' },\n            { orderNo: 'ORD20250128003', amount: '89.90', status: '待付款' }\n          ]\n        }, 1000)\n      } catch (error) {\n        console.error('加载数据失败:', error)\n      }\n    },\n    \n    updateStatsData(data) {\n      this.statsData[0].value = data.todayOrders || '0'\n      this.statsData[1].value = `¥${data.todaySales || '0'}`\n      this.statsData[2].value = data.totalUsers || '0'\n      this.statsData[3].value = data.totalProducts || '0'\n    },\n    \n    handleAction(action) {\n      switch (action.action) {\n        case 'orders':\n          uni.switchTab({ url: '/pages/order/index' })\n          break\n        case 'products':\n          uni.switchTab({ url: '/pages/store/index' })\n          break\n        case 'users':\n          uni.switchTab({ url: '/pages/user/index' })\n          break\n        case 'marketing':\n          uni.showToast({\n            title: '功能开发中',\n            icon: 'none'\n          })\n          break\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.dashboard {\n  padding: 20rpx;\n  background: $crmeb-bg-color;\n  min-height: 100vh;\n}\n\n.header {\n  background: white;\n  padding: 40rpx;\n  border-radius: 16rpx;\n  margin-bottom: 20rpx;\n  \n  .welcome {\n    display: block;\n    font-size: 28rpx;\n    color: #999;\n    margin-bottom: 10rpx;\n  }\n  \n  .username {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.stats-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr;\n  gap: 20rpx;\n  margin-bottom: 20rpx;\n}\n\n.stat-item {\n  background: white;\n  padding: 40rpx;\n  border-radius: 16rpx;\n  position: relative;\n  \n  .stat-value {\n    font-size: 48rpx;\n    font-weight: bold;\n    color: $crmeb-color-primary;\n    margin-bottom: 10rpx;\n  }\n  \n  .stat-label {\n    font-size: 24rpx;\n    color: #999;\n  }\n  \n  .stat-icon {\n    position: absolute;\n    top: 20rpx;\n    right: 20rpx;\n    font-size: 40rpx;\n  }\n}\n\n.quick-actions, .recent-orders {\n  background: white;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  margin-bottom: 20rpx;\n}\n\n.section-title {\n  font-size: 32rpx;\n  font-weight: bold;\n  color: #333;\n  margin-bottom: 30rpx;\n}\n\n.action-grid {\n  display: grid;\n  grid-template-columns: 1fr 1fr 1fr 1fr;\n  gap: 20rpx;\n}\n\n.action-item {\n  text-align: center;\n  padding: 20rpx;\n  \n  .action-icon {\n    font-size: 48rpx;\n    margin-bottom: 10rpx;\n  }\n  \n  .action-label {\n    font-size: 24rpx;\n    color: #666;\n  }\n}\n\n.order-list {\n  .order-item {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 20rpx 0;\n    border-bottom: 1rpx solid #f0f0f0;\n    \n    &:last-child {\n      border-bottom: none;\n    }\n    \n    .order-info {\n      .order-no {\n        display: block;\n        font-size: 28rpx;\n        color: #333;\n        margin-bottom: 10rpx;\n      }\n      \n      .order-amount {\n        font-size: 24rpx;\n        color: $crmeb-color-primary;\n        font-weight: bold;\n      }\n    }\n    \n    .order-status {\n      font-size: 24rpx;\n      color: $crmeb-color-success;\n      padding: 10rpx 20rpx;\n      background: rgba(25, 190, 107, 0.1);\n      border-radius: 20rpx;\n    }\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=65dd1eae&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=65dd1eae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685164115\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}