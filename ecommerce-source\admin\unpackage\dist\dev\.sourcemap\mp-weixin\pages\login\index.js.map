{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?7f6f", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?755a", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?2531", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?f2fe", "uni-app:///pages/login/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?ee3d", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/login/index.vue?f6f1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loading", "loginForm", "account", "pwd", "methods", "callLoginApi", "uni", "url", "method", "header", "success", "resolve", "reject", "fail", "handleLogin", "title", "icon", "response", "console"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCsC77B;EACAC;IACA;MACAC;MACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,iCACA;kBACAC;oBACAC;oBACAC;oBACAT;oBACAU;sBACA;oBACA;oBACAC;sBACA;wBACAC;sBACA;wBACAC;sBACA;oBACA;oBACAC;sBACAD;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAS;kBACAC;gBACA;gBAAA;cAAA;gBAIA;gBAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAC;gBAEA;kBACA;kBACAX;kBACAA;kBAEAA;oBACAS;oBACAC;kBACA;;kBAEA;kBACAV;oBACAC;kBACA;gBACA;kBACAD;oBACAS;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAE;gBACAZ;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtHA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4586967a&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4586967a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4586967a&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"login-container\">\n    <view class=\"login-form\">\n      <view class=\"logo\">\n        <image src=\"/static/logo.png\" mode=\"aspectFit\"></image>\n        <text class=\"title\">CRMEB管理后台</text>\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          v-model=\"loginForm.account\" \n          placeholder=\"请输入账号\" \n          class=\"input\"\n          type=\"text\"\n        />\n      </view>\n      \n      <view class=\"form-item\">\n        <input \n          v-model=\"loginForm.pwd\" \n          placeholder=\"请输入密码\" \n          class=\"input\"\n          type=\"password\"\n        />\n      </view>\n      \n      <button \n        class=\"login-btn\" \n        @click=\"handleLogin\"\n        :loading=\"loading\"\n      >\n        登录\n      </button>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      loading: false,\n      loginForm: {\n        account: '',\n        pwd: ''\n      }\n    }\n  },\n  methods: {\n    async callLoginApi(data) {\n      return new Promise((resolve, reject) => {\n        uni.request({\n          url: 'http://localhost:20500/api/admin/login',\n          method: 'POST',\n          data: data,\n          header: {\n            'Content-Type': 'application/json'\n          },\n          success: (res) => {\n            if (res.statusCode === 200) {\n              resolve(res.data)\n            } else {\n              reject(new Error('登录失败'))\n            }\n          },\n          fail: (err) => {\n            reject(err)\n          }\n        })\n      })\n    },\n\n    async handleLogin() {\n      if (!this.loginForm.account || !this.loginForm.pwd) {\n        uni.showToast({\n          title: '请输入账号和密码',\n          icon: 'none'\n        })\n        return\n      }\n      \n      this.loading = true\n      \n      try {\n        // 调用登录API\n        const response = await this.callLoginApi(this.loginForm)\n        \n        if (response.status === 200) {\n          // 保存token\n          uni.setStorageSync('token', response.data.token)\n          uni.setStorageSync('userInfo', response.data)\n          \n          uni.showToast({\n            title: '登录成功',\n            icon: 'success'\n          })\n          \n          // 跳转到首页\n          uni.switchTab({\n            url: '/pages/dashboard/index'\n          })\n        } else {\n          uni.showToast({\n            title: response.message || '登录失败',\n            icon: 'none'\n          })\n        }\n      } catch (error) {\n        console.error('登录错误:', error)\n        uni.showToast({\n          title: '网络错误，请重试',\n          icon: 'none'\n        })\n      } finally {\n        this.loading = false\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.login-container {\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 40rpx;\n}\n\n.login-form {\n  background: white;\n  border-radius: 20rpx;\n  padding: 80rpx 60rpx;\n  width: 100%;\n  max-width: 600rpx;\n  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);\n}\n\n.logo {\n  text-align: center;\n  margin-bottom: 80rpx;\n  \n  image {\n    width: 120rpx;\n    height: 120rpx;\n    margin-bottom: 20rpx;\n  }\n  \n  .title {\n    display: block;\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.form-item {\n  margin-bottom: 40rpx;\n}\n\n.input {\n  width: 100%;\n  height: 88rpx;\n  border: 2rpx solid #e4e7ed;\n  border-radius: 8rpx;\n  padding: 0 20rpx;\n  font-size: 28rpx;\n  box-sizing: border-box;\n  \n  &:focus {\n    border-color: $crmeb-color-primary;\n  }\n}\n\n.login-btn {\n  width: 100%;\n  height: 88rpx;\n  background: $crmeb-color-primary;\n  color: white;\n  border: none;\n  border-radius: 8rpx;\n  font-size: 32rpx;\n  font-weight: bold;\n  margin-top: 40rpx;\n  \n  &:hover {\n    background: darken($crmeb-color-primary, 10%);\n  }\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.**********\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4586967a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685164104\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.**********/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}