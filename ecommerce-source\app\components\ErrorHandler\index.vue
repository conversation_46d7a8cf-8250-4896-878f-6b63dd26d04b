<template>
  <view class="error-handler" v-if="visible">
    <!-- 网络错误 -->
    <view v-if="errorType === 'network'" class="error-content network-error">
      <image class="error-icon" src="/static/icons/network-error.png" mode="aspectFit"></image>
      <text class="error-title">网络连接异常</text>
      <text class="error-message">{{ errorMessage || '请检查网络连接后重试' }}</text>
      <view class="error-actions">
        <button class="retry-btn" @click="handleRetry">重新加载</button>
        <button class="offline-btn" @click="handleOfflineMode">离线浏览</button>
      </view>
    </view>
    
    <!-- 服务器错误 -->
    <view v-else-if="errorType === 'server'" class="error-content server-error">
      <image class="error-icon" src="/static/icons/server-error.png" mode="aspectFit"></image>
      <text class="error-title">服务器繁忙</text>
      <text class="error-message">{{ errorMessage || '服务器暂时无法响应，请稍后重试' }}</text>
      <view class="error-actions">
        <button class="retry-btn" @click="handleRetry">重试</button>
        <button class="contact-btn" @click="handleContact">联系客服</button>
      </view>
    </view>
    
    <!-- 认证错误 -->
    <view v-else-if="errorType === 'auth'" class="error-content auth-error">
      <image class="error-icon" src="/static/icons/auth-error.png" mode="aspectFit"></image>
      <text class="error-title">登录已过期</text>
      <text class="error-message">{{ errorMessage || '请重新登录后继续使用' }}</text>
      <view class="error-actions">
        <button class="login-btn" @click="handleLogin">重新登录</button>
        <button class="guest-btn" @click="handleGuestMode">游客浏览</button>
      </view>
    </view>
    
    <!-- 业务错误 -->
    <view v-else-if="errorType === 'business'" class="error-content business-error">
      <image class="error-icon" src="/static/icons/warning.png" mode="aspectFit"></image>
      <text class="error-title">操作失败</text>
      <text class="error-message">{{ errorMessage || '操作失败，请重试' }}</text>
      <view class="error-actions">
        <button class="retry-btn" @click="handleRetry">重试</button>
        <button class="back-btn" @click="handleBack">返回</button>
      </view>
    </view>
    
    <!-- 通用错误 -->
    <view v-else class="error-content generic-error">
      <image class="error-icon" src="/static/icons/error.png" mode="aspectFit"></image>
      <text class="error-title">出现错误</text>
      <text class="error-message">{{ errorMessage || '系统出现错误，请稍后重试' }}</text>
      <view class="error-actions">
        <button class="retry-btn" @click="handleRetry">重试</button>
        <button class="refresh-btn" @click="handleRefresh">刷新页面</button>
      </view>
    </view>
    
    <!-- 错误详情（开发模式） -->
    <view v-if="showDetails && isDevelopment" class="error-details">
      <text class="details-title">错误详情（开发模式）</text>
      <text class="details-content">{{ errorDetails }}</text>
    </view>
  </view>
</template>

<script>
import EnvironmentManager from '@/config/environment';

export default {
  name: 'ErrorHandler',
  props: {
    // 错误类型：network, server, auth, business, generic
    errorType: {
      type: String,
      default: 'generic'
    },
    // 错误消息
    errorMessage: {
      type: String,
      default: ''
    },
    // 错误详情（开发模式显示）
    errorDetails: {
      type: String,
      default: ''
    },
    // 是否显示
    visible: {
      type: Boolean,
      default: false
    },
    // 是否显示详情
    showDetails: {
      type: Boolean,
      default: false
    },
    // 重试回调
    onRetry: {
      type: Function,
      default: null
    },
    // 自定义操作
    customActions: {
      type: Array,
      default: () => []
    }
  },
  
  computed: {
    isDevelopment() {
      return EnvironmentManager.isDevelopment();
    }
  },
  
  methods: {
    // 重试操作
    handleRetry() {
      this.$monitor.reportUserAction('error_retry', {
        errorType: this.errorType,
        errorMessage: this.errorMessage
      });
      
      if (this.onRetry && typeof this.onRetry === 'function') {
        this.onRetry();
      } else {
        this.$emit('retry');
      }
    },
    
    // 离线模式
    handleOfflineMode() {
      this.$monitor.reportUserAction('error_offline_mode');
      this.$emit('offline-mode');
    },
    
    // 联系客服
    handleContact() {
      this.$monitor.reportUserAction('error_contact_service');
      // 这里可以跳转到客服页面或打开客服对话
      uni.navigateTo({
        url: '/pages/customer-service/index'
      });
    },
    
    // 重新登录
    handleLogin() {
      this.$monitor.reportUserAction('error_relogin');
      this.$emit('login');
      // 跳转到登录页面
      uni.navigateTo({
        url: '/pages/login/index'
      });
    },
    
    // 游客模式
    handleGuestMode() {
      this.$monitor.reportUserAction('error_guest_mode');
      this.$emit('guest-mode');
    },
    
    // 返回上一页
    handleBack() {
      this.$monitor.reportUserAction('error_back');
      uni.navigateBack({
        delta: 1
      });
    },
    
    // 刷新页面
    handleRefresh() {
      this.$monitor.reportUserAction('error_refresh');
      // #ifdef H5
      location.reload();
      // #endif
      // #ifndef H5
      uni.reLaunch({
        url: getCurrentPages()[getCurrentPages().length - 1].route
      });
      // #endif
    }
  }
};
</script>

<style lang="scss" scoped>
.error-handler {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 60rpx 40rpx;
  background-color: #f8f9fa;
}

.error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 600rpx;
}

.error-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
}

.error-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 40rpx;
}

.error-actions {
  display: flex;
  gap: 20rpx;
  flex-wrap: wrap;
  justify-content: center;
}

.retry-btn, .offline-btn, .contact-btn, .login-btn, 
.guest-btn, .back-btn, .refresh-btn {
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.retry-btn, .login-btn {
  background-color: #007aff;
  color: white;
}

.offline-btn, .guest-btn, .back-btn {
  background-color: #f0f0f0;
  color: #333;
}

.contact-btn, .refresh-btn {
  background-color: #34c759;
  color: white;
}

.error-details {
  margin-top: 40rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 8rpx;
  border: 1px solid #e0e0e0;
  width: 100%;
  max-width: 600rpx;
}

.details-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #666;
  margin-bottom: 10rpx;
  display: block;
}

.details-content {
  font-size: 22rpx;
  color: #999;
  font-family: monospace;
  word-break: break-all;
  line-height: 1.4;
}

/* 不同错误类型的主题色 */
.network-error .error-title {
  color: #ff9500;
}

.server-error .error-title {
  color: #ff3b30;
}

.auth-error .error-title {
  color: #007aff;
}

.business-error .error-title {
  color: #ff9500;
}

.generic-error .error-title {
  color: #8e8e93;
}
</style>
