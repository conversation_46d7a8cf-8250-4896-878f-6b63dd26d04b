<view class="store-page data-v-38de483b"><view class="search-bar data-v-38de483b"><input class="search-input data-v-38de483b" placeholder="搜索商品名称" data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn data-v-38de483b" bindtap="__e">搜索</button></view><view class="filter-tabs data-v-38de483b"><block wx:for="{{statusTabs}}" wx:for-item="item" wx:for-index="__i0__" wx:key="value"><view data-event-opts="{{[['tap',[['switchTab',['$0'],[[['statusTabs','value',item.value,'value']]]]]]]}}" class="{{['tab-item','data-v-38de483b',(currentTab===item.value)?'active':'']}}" bindtap="__e">{{''+item.label+''}}</view></block></view><view class="product-list data-v-38de483b"><block wx:for="{{productList}}" wx:for-item="product" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewProductDetail',['$0'],[[['productList','',index]]]]]]]}}" class="product-item data-v-38de483b" bindtap="__e"><view class="product-image data-v-38de483b"><image src="{{product.image||'/static/default-product.png'}}" mode="aspectFill" class="data-v-38de483b"></image></view><view class="product-info data-v-38de483b"><view class="product-name data-v-38de483b">{{product.storeName}}</view><view class="product-price data-v-38de483b"><text class="current-price data-v-38de483b">{{"¥"+product.price}}</text><block wx:if="{{product.otPrice>product.price}}"><text class="original-price data-v-38de483b">{{"¥"+product.otPrice}}</text></block></view><view class="product-stats data-v-38de483b"><text class="stock data-v-38de483b">{{"库存："+product.stock}}</text><text class="sales data-v-38de483b">{{"销量："+(product.sales||0)}}</text></view></view><view class="product-status data-v-38de483b"><view class="{{['status-badge','data-v-38de483b',product.isShow?'status-online':'status-offline']}}">{{''+(product.isShow?'上架':'下架')+''}}</view><view class="product-actions data-v-38de483b"><button data-event-opts="{{[['tap',[['toggleProductStatus',['$0'],[[['productList','',index]]]]]]]}}" class="action-btn data-v-38de483b" catchtap="__e">{{''+(product.isShow?'下架':'上架')+''}}</button></view></view></view></block></view><view data-event-opts="{{[['tap',[['addProduct',['$event']]]]]}}" class="add-product-btn data-v-38de483b" bindtap="__e"><text class="data-v-38de483b">+</text></view><block wx:if="{{loading}}"><view class="loading data-v-38de483b"><text class="data-v-38de483b">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty data-v-38de483b"><text class="data-v-38de483b">暂无商品数据</text></view></block></view>