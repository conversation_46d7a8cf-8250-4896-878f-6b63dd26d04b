<template>
  <view class="optimized-image" :style="containerStyle">
    <!-- 加载状态 -->
    <view v-if="loading" class="image-loading">
      <view class="loading-placeholder" :style="placeholderStyle">
        <view class="loading-spinner"></view>
        <text v-if="showLoadingText" class="loading-text">加载中...</text>
      </view>
    </view>
    
    <!-- 错误状态 -->
    <view v-else-if="error" class="image-error" :style="placeholderStyle" @click="handleRetry">
      <image class="error-icon" src="/static/icons/image-error.png" mode="aspectFit"></image>
      <text class="error-text">图片加载失败</text>
      <text class="retry-text">点击重试</text>
    </view>
    
    <!-- 图片内容 -->
    <image
      v-else
      :src="optimizedSrc"
      :mode="mode"
      :lazy-load="lazyLoad"
      :fade-show="fadeShow"
      :webp="webp"
      :show-menu-by-longpress="showMenuByLongpress"
      :style="imageStyle"
      @load="handleLoad"
      @error="handleError"
      @click="handleClick"
      class="optimized-image-content"
    />
    
    <!-- 预览遮罩 -->
    <view v-if="previewable && !loading && !error" class="preview-mask" @click="handlePreview">
      <view class="preview-icon">
        <text class="iconfont icon-zoom-in"></text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'OptimizedImage',
  props: {
    // 图片源地址
    src: {
      type: String,
      required: true
    },
    // 显示模式
    mode: {
      type: String,
      default: 'aspectFill'
    },
    // 宽度
    width: {
      type: [String, Number],
      default: 'auto'
    },
    // 高度
    height: {
      type: [String, Number],
      default: 'auto'
    },
    // 是否懒加载
    lazyLoad: {
      type: Boolean,
      default: true
    },
    // 是否渐显
    fadeShow: {
      type: Boolean,
      default: true
    },
    // 是否支持WebP
    webp: {
      type: Boolean,
      default: true
    },
    // 是否支持长按菜单
    showMenuByLongpress: {
      type: Boolean,
      default: false
    },
    // 是否可预览
    previewable: {
      type: Boolean,
      default: false
    },
    // 预览图片列表
    previewUrls: {
      type: Array,
      default: () => []
    },
    // 当前预览索引
    previewIndex: {
      type: Number,
      default: 0
    },
    // 占位图
    placeholder: {
      type: String,
      default: '/static/images/placeholder.png'
    },
    // 是否显示加载文本
    showLoadingText: {
      type: Boolean,
      default: false
    },
    // 图片质量优化
    quality: {
      type: Number,
      default: 80,
      validator: value => value >= 1 && value <= 100
    },
    // 是否启用CDN优化
    enableCdn: {
      type: Boolean,
      default: true
    },
    // 圆角
    borderRadius: {
      type: [String, Number],
      default: 0
    }
  },
  
  data() {
    return {
      loading: true,
      error: false,
      loadStartTime: 0
    };
  },
  
  computed: {
    // 优化后的图片地址
    optimizedSrc() {
      if (!this.src) return this.placeholder;
      
      let url = this.src;
      
      // CDN优化
      if (this.enableCdn && this.src.includes('crmebimage')) {
        url = this.applyCdnOptimization(url);
      }
      
      return url;
    },
    
    // 容器样式
    containerStyle() {
      const style = {
        position: 'relative',
        display: 'inline-block',
        overflow: 'hidden'
      };
      
      if (this.width !== 'auto') {
        style.width = typeof this.width === 'number' ? this.width + 'rpx' : this.width;
      }
      
      if (this.height !== 'auto') {
        style.height = typeof this.height === 'number' ? this.height + 'rpx' : this.height;
      }
      
      if (this.borderRadius) {
        style.borderRadius = typeof this.borderRadius === 'number' ? 
          this.borderRadius + 'rpx' : this.borderRadius;
      }
      
      return style;
    },
    
    // 占位符样式
    placeholderStyle() {
      return {
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: '#f5f5f5'
      };
    },
    
    // 图片样式
    imageStyle() {
      return {
        width: '100%',
        height: '100%',
        display: 'block'
      };
    }
  },
  
  watch: {
    src: {
      handler(newSrc) {
        if (newSrc) {
          this.resetState();
        }
      },
      immediate: true
    }
  },
  
  methods: {
    // 重置状态
    resetState() {
      this.loading = true;
      this.error = false;
      this.loadStartTime = Date.now();
    },
    
    // CDN优化
    applyCdnOptimization(url) {
      try {
        // 添加质量参数
        const separator = url.includes('?') ? '&' : '?';
        let optimizedUrl = `${url}${separator}quality=${this.quality}`;
        
        // 添加格式优化
        if (this.webp) {
          optimizedUrl += '&format=webp';
        }
        
        // 添加尺寸优化（如果指定了宽高）
        if (this.width !== 'auto' && this.height !== 'auto') {
          const width = typeof this.width === 'number' ? this.width : parseInt(this.width);
          const height = typeof this.height === 'number' ? this.height : parseInt(this.height);
          optimizedUrl += `&w=${width}&h=${height}`;
        }
        
        return optimizedUrl;
      } catch (error) {
        console.warn('CDN优化失败:', error);
        return url;
      }
    },
    
    // 图片加载成功
    handleLoad(event) {
      const loadTime = Date.now() - this.loadStartTime;
      
      this.loading = false;
      this.error = false;
      
      // 性能监控
      this.$performance.reportPerformance({
        name: 'image_load_time',
        value: loadTime,
        url: this.src
      });
      
      this.$emit('load', event);
    },
    
    // 图片加载失败
    handleError(event) {
      this.loading = false;
      this.error = true;
      
      // 错误监控
      this.$monitor.reportError(new Error('Image load failed'), {
        type: 'image_load_error',
        src: this.src,
        optimizedSrc: this.optimizedSrc
      });
      
      this.$emit('error', event);
    },
    
    // 重试加载
    handleRetry() {
      this.$monitor.reportUserAction('image_retry', {
        src: this.src
      });
      
      this.resetState();
      this.$forceUpdate();
    },
    
    // 图片点击
    handleClick(event) {
      if (this.previewable && !this.loading && !this.error) {
        this.handlePreview();
      } else {
        this.$emit('click', event);
      }
    },
    
    // 预览图片
    handlePreview() {
      const urls = this.previewUrls.length > 0 ? this.previewUrls : [this.src];
      const current = this.previewUrls.length > 0 ? this.previewIndex : 0;
      
      this.$monitor.reportUserAction('image_preview', {
        src: this.src,
        urls: urls.length
      });
      
      uni.previewImage({
        urls: urls,
        current: current,
        fail: (error) => {
          console.warn('图片预览失败:', error);
          this.$monitor.reportError(new Error('Image preview failed'), {
            type: 'image_preview_error',
            src: this.src
          });
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.optimized-image {
  position: relative;
  display: inline-block;
  overflow: hidden;
}

.image-loading {
  width: 100%;
  height: 100%;
}

.loading-placeholder {
  background-color: #f5f5f5;
  color: #999;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 3rpx solid #f3f3f3;
  border-top: 3rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10rpx;
}

.loading-text {
  font-size: 24rpx;
  color: #999;
}

.image-error {
  background-color: #f5f5f5;
  color: #999;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.image-error:hover {
  background-color: #eeeeee;
}

.error-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.error-text {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 5rpx;
}

.retry-text {
  font-size: 20rpx;
  color: #007aff;
}

.optimized-image-content {
  display: block;
  width: 100%;
  height: 100%;
}

.preview-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.optimized-image:hover .preview-mask {
  background-color: rgba(0, 0, 0, 0.3);
  opacity: 1;
}

.preview-icon {
  width: 60rpx;
  height: 60rpx;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  font-size: 32rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
