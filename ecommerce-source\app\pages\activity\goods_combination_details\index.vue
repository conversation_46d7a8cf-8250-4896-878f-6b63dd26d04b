<template>
	<view :data-theme="theme">
		<skeleton :show="showSkeleton" :isNodes="isNodes" ref="skeleton" loading="chiaroscuro" selector="skeleton"
			bgcolor="#FFF"></skeleton>
		<view class="skeleton" :style="{visibility: showSkeleton ? 'hidden' : 'visible'}">
			<!-- 头部 -->
			<view class='navbar' :class="opacity>0.6?'bgwhite':''">
				<view class='navbarH' :style='"height:"+navH+"rpx;"'>
					<view class='navbarCon acea-row' :style="{ paddingRight: navbarRight + 'px' }">
						<!-- #ifdef MP -->
						<view class="select_nav flex justify-center align-center" id="home" :style="{ top: homeTop + 'rpx' }">
							<text class="iconfont icon-fanhui2 px-20" @tap="returns"></text>
							<text class="iconfont icon-gengduo5 px-20" @tap="showNav"></text>
							<text class="nav_line"></text>
						</view>
						<!-- #endif -->
						<!-- #ifdef H5 || APP-PLUS -->
						<view id="home" class="home acea-row row-center-wrapper iconfont icon-xiangzuo h5_back" :class="opacity>0.5?'on':''"
							:style="{ top: homeTop + 'rpx' }" v-if="returnShow" @tap="returns">
						</view>
						<!-- #endif -->
						<view class="header flex justify-between align-center">
							<view class="item" :class="navActive === index ? 'on' : ''" v-for="(item,index) in navList"
								:key='index' @tap="tap(item,index)">
								{{ item }}
							</view>
						</view>
						<!-- #ifdef H5 || APP-PLUS -->
						<view class="right_select" :style="{ top: homeTop + 'rpx' }" @tap="showNav">
							<text class="iconfont icon-gengduo2"></text>
						</view>
						<!-- #endif -->
					</view>
				</view>
			</view>
			<view class="dialog_nav" :style='"top:"+navH+"rpx;"' v-show="currentPage">
				<view class="dialog_nav_item" :class="item.after" v-for="(item,index) in selectNavList" :key="index" @click="linkPage(item.url)">
					<text class="iconfont" :class="item.icon"></text>
					<text class="pl-20">{{item.name}}</text>
				</view>
			</view>
			<view class='product-con'>
				<scroll-view :scroll-top="scrollTop" scroll-y='true' scroll-with-animation="true"
					:style="'height:'+height+'px;'" @scroll="scroll" enable-flex="true">
					<view id="past0">
						<productConSwiper :imgUrls="imgUrls" class="mb30 skeleton-rect" :videoline="videoLink"></productConSwiper>
						<view class="pad30">
							<view class='wrapper mb30'>
								<view class='share acea-row row-between row-bottom'>
									<view class='money font_color'>
										￥<text class='num skeleton-rect'>{{attribute.productSelect.price || 0}}</text><text
											class='y-money skeleton-rect'>￥{{storeInfo.otPrice || 0}}</text>
									</view>
									<view class='iconfont icon-fenxiang' @click="listenerActionSheet"></view>
								</view>
								<view class='introduce line2 skeleton-rect'>{{storeInfo.storeName}}</view>
								<view class='label acea-row row-between-wrapper'>
									<view class='stock skeleton-rect'>类型：{{storeInfo.people || 0}}人团</view>
									<view class="skeleton-rect">累计销量：{{parseFloat(storeInfo.sales)  + parseFloat(storeInfo.ficti)}} {{storeInfo.unitName || ''}}</view>
									<view class="skeleton-rect">限购: {{ attribute.productSelect.quotaShow ? attribute.productSelect.quotaShow : 0 }}
										{{storeInfo.unitName || ''}}
									</view>
								</view>
							</view>
							<view class='attribute mb30 borRadius14' @click="selecAttr">
								<view class="acea-row row-between-wrapper">
									<view class="line1 skeleton-rect">{{attrTxt}}：
										<text class='atterTxt'>{{attrValue}}</text>
									</view>
									<view class='iconfont icon-jiantou'></view>
								</view>
								<view class="acea-row row-between-wrapper" style="margin-top:7px;padding-left:55px;" v-if="skuArr.length > 1">
									<view class="flex">
										<image :src="item.image" v-for="(item,index) in skuArr.slice(0,4)" :key="index" class="attrImg"></image>
									</view>
									<view class="switchTxt">共{{skuArr.length}}种规格可选</view>
								</view>
							</view>
							<view class='notice acea-row row-middle mb30 borRadius14' v-if="parseFloat(pinkOkSum) >0">
								<view class='num font-color skeleton-rect'>
									<text class='iconfont icon-laba'></text>
									已拼{{pinkOkSum}}件<text class='line'>|</text>
								</view>
								<view class='swiper'>
									<swiper :indicator-dots="indicatorDots" :autoplay="autoplay" interval="2500"
										duration="500" vertical="true" circular="true">
										<block v-for="(item,index) in itemNew" :key='index'>
											<swiper-item>
												<view class='line1'>{{item.nickname}}拼团成功</view>
											</swiper-item>
										</block>
									</swiper>
								</view>
							</view>
							<view v-if='attribute.productSelect.quota > 0' class='assemble mb30 borRadius14'>
								<view class='item acea-row row-between-wrapper' v-for='(item,index) in pink' :key='index'
									v-if="index < AllIndex">
									<view class='pictxt acea-row row-between-wrapper'>
										<view class='pictrue'>
											<image :src='item.avatar'></image>
										</view>
										<view class='text line1'>{{item.nickname}}</view>
									</view>
									<view class='right acea-row row-middle'>
										<view>
											<view class='lack'>还差<text class='font-color'>{{item.count}}</text>人成团</view>
											<view class='time acea-row'>
												<count-down :is-day="false" :tip-text="'剩余 '" :day-text="' '" :hour-text="':'"
													:minute-text="':'" :second-text="' '" :datatime="item.stopTime/1000" :bgColor="bgColor">
												</count-down>
											</view>
										</view>
										<navigator hover-class='none'
											:url="'/pages/activity/goods_combination_status/index?id='+item.id"
											class='spellBnt'>
											去拼单
											<text class='iconfont icon-jiantou'></text>
										</navigator>
									</view>
								</view>
								<template v-if="pink.length">
									<view class='more' @tap='showAll' v-if="pink.length > AllIndex">查看更多<text
											class='iconfont icon-xiangxia'></text></view>
									<view class='more' @tap='hideAll'
										v-else-if="pink.length === AllIndex && pink.length !== AllIndexDefault">收起<text
											class='iconfont icon-xiangshang'></text></view>
								</template>
							</view>
							<view class='playWay mb30 borRadius14'>
								<view class='title acea-row row-between row-middle'>
									<view>拼团玩法</view>
									<!-- <navigator hover-class='none' class='font-color' url='/pages/activity/goods_combination_rule/index'>查看规则<text class="iconfont icon-jiantou"></text></navigator> -->
								</view>
								<view class='way acea-row row-middle'>
									<view class='item acea-row row-middle'>
										<text class='num mb-02'>①</text>
										开团/参团
									</view>
									<view class='iconfont icon-arrow'></view>
									<view class='item'>
										<text class='num'>②</text>
										邀请好友
									</view>
									<view class='iconfont icon-arrow'></view>
									<view class='item'>
										<view>
											<text class='num'>③</text>
											满员发货
										</view>
										<!-- <view class='tip'>不满自动退款</view> -->
									</view>
								</view>
							</view>
							<view class='userEvaluation borRadius14' id="past1">
								<view class='title acea-row row-between-wrapper' :style="replyCount==0?'border-bottom-left-radius:14rpx;border-bottom-right-radius:14rpx;':''">
									<view>用户评价<i>({{replyCount}})</i></view>
									<navigator class='praise' hover-class='none'
										:url='"/pages/goods/goods_comment_list/index?productId="+storeInfo.productId'>
										<i>好评</i><text class='font_color pl-14'>{{replyChance || 0}}%</text>
										
										<text class='iconfont icon-jiantou'></text>
									</navigator>
								</view>
								<userEvaluation :reply="reply" v-if="reply.length>0"></userEvaluation>
							</view>
						</view>
					</view>
					
					<view class='product-intro' id="past2">
						<view class='title'>
							<image :src="urlDomain+'crmebimage/perset/staticImg/xzuo.png'"></image>
							<span class="sp">产品详情</span>
							<image :src="urlDomain+'crmebimage/perset/staticImg/xyou.png'"></image>
						</view>
						<view class='conter'>
							<jyf-parser :html="storeInfo.content" ref="article" :tag-style="tagStyle"></jyf-parser>
						</view>
					</view>
					<view style='height:120rpx;'></view>
				</scroll-view>
				<view class='footer acea-row row-between-wrapper'>
					<!-- #ifdef MP -->
					<button hover-class='none' class='item skeleton-rect' @click="onClickService"
						v-if="chatConfig.telephone_service_switch === 'open'">
						<view class='iconfont icon-kefu'></view>
						<view>客服</view>
					</button>
					<template v-else>
						<button open-type="contact" hover-class='none' class='item skeleton-rect' v-if="chatConfig.wx_chant_independent=='open'">
							<view class='iconfont icon-kefu'></view>
							<view>客服</view>
						</button>
						<button class="item" hover-class='none' @click="wxChatService"  v-else>
							<view class='iconfont icon-kefu'></view>
							<text>联系客服</text>
						</button>
					</template>
					<!-- #endif -->
					<!-- #ifndef MP -->
					<navigator hover-class="none" class="item skeleton-rect" @click="onClickService">
						<view class="iconfont icon-kefu"></view>
						<view>客服</view>
					</navigator>
					<!-- #endif -->
					<view @tap='setCollect' class='item skeleton-rect'>
						<view class='iconfont icon-shoucang1' v-if="userCollect"></view>
						<view class='iconfont icon-shoucang' v-else></view>
						<view>收藏</view>
					</view>
					<view class="bnt acea-row skeleton-rect">
						<view class="joinCart bnts" @tap="goProduct" v-if="masterStatus !=='soldOut'">单独购买</view>
						<view class=" bg-color-hui bnts radius" v-if="masterStatus =='soldOut'">单独购买</view>
						<view class="buy bnts" @tap="goCat"
							v-if='attribute.productSelect.quota>0'>
							立即开团
						</view>
						<view class="buy bnts bg-color-hui" v-if="!dataShow">
							立即开团
						</view>
						<view class=" bnts buy-btns bg-color-hui"
							v-if='attribute.productSelect.quota <= 0'>
							已售罄
						</view>
					</view>
				</view>
			</view>
			<shareRedPackets :sharePacket="sharePacket" @listenerActionSheet="listenerActionSheet"
				@closeChange="closeChange"></shareRedPackets>
			<!-- 分享按钮 -->
			<view class="generate-posters" :class="posters ? 'on' : ''">
				<view class="generateCon acea-row row-middle">
					<!-- #ifndef MP -->
					<button class="item" hover-class="none" v-if="weixinStatus === true" @click="H5ShareBox = true">
						<view class="pictrue">
							<image src="/static/images/weixin.png"></image>
						</view>
						<view class="">分享给好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef MP -->
					<button class="item" open-type="share" hover-class="none">
						<view class="pictrue">
							<image src="/static/images/weixin.png"></image>
						</view>
						<view class="">分享给好友</view>
					</button>
					<!-- #endif -->
					<!-- #ifdef APP-PLUS -->
					<view class="item" @click="appShare('WXSceneSession')">
						<view class="iconfont icon-weixin3"></view>
						<view class="">微信好友</view>
					</view>
					<view class="item" @click="appShare('WXSenceTimeline')">
						<view class="iconfont icon-pengyouquan"></view>
						<view class="">微信朋友圈</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef H5 || MP -->
					<view class="item" @click="getpreviewImage">
						<view class="pictrue">
							<image :src="urlDomain+'crmebimage/perset/staticImg/changan.png'"></image>
						</view>
						<view class="">预览发图</view>
					</view>
					<!-- #endif -->
					<!-- #ifdef MP  -->
					<button class="item" hover-class="none" @click="savePosterPath">
						<view class="pictrue">
							<image src="/static/images/haibao.png"></image>
						</view>
						<view class="">保存海报</view>
					</button>
					<!-- #endif -->
				</view>
				<view class="generateClose acea-row row-center-wrapper" @click="posterImageClose">取消</view>
			</view>
			<cus-previewImg ref="cusPreviewImg" :list="skuArr" @changeSwitch="changeSwitch" />
			<view class="mask" v-if="posters" @click="closePosters"></view>
			<view class="mask" v-if="canvasStatus" @click="listenerActionClose"></view>
			<view class="mask_transparent" v-if="currentPage" @touchmove="hideNav" @click="hideNav()"></view>
			
			<!-- 海报展示 -->
			<view class='poster-pop' v-if="canvasStatus">
				<image :src='posterImage'></image>
			</view>
			<view class="canvas" v-else>
				<canvas style="width:750px;height:1190px;" canvas-id="firstCanvas"></canvas>
				<canvas canvas-id="qrcode" :style="{width: `${qrcodeSize}px`, height: `${qrcodeSize}px`}" />
			</view>
			<!-- 发送给朋友图片 -->
			<view class="share-box" v-if="H5ShareBox">
				<image src="/static/images/share-info.png" @click="H5ShareBox = false"></image>
			</view>
			<product-window :attr='attribute' :limitNum='1' @myevent="onMyEvent" @ChangeAttr="ChangeAttr"
				@ChangeCartNum="ChangeCartNum" @iptCartNum="iptCartNum" @attrVal="attrVal"  @getImg="showImg"></product-window>
		</view>	

	</view>
</template>

<script>
	const app = getApp();
	import uQRCode from '@/js_sdk/Sansnn-uQRCode/uqrcode.js'
	import {mapGetters} from "vuex";
	import { silenceBindingSpread } from "@/utils";
	// #ifdef APP-PLUS
	import {HTTP_H5_URL} from '@/config/app.js';
	// #endif
	// #ifdef MP
	import {base64src} from '@/utils/base64src.js'
	import {getQrcode} from '@/api/api.js';
	// #endif
	import productConSwiper from '@/components/productConSwiper'
	import {toLogin} from '@/libs/login.js';
	import {getCombinationDetail} from '@/api/activity.js';
	import {
		postCartAdd,
		collectAdd,
		collectDel,
		getReplyList,
		getReplyConfig,
		getReplyProduct
	} from '@/api/store.js';
	import {imageBase64} from "@/api/public";
	import { spread } from "@/api/user";
	import parser from "@/components/jyf-parser/jyf-parser";
	import productWindow from '@/components/productWindow/index.vue'
	import userEvaluation from '@/components/userEvaluation/index.vue'
	import countDown from '@/components/countDown/index.vue'
	import shareRedPackets from '@/components/shareRedPackets';
	import cusPreviewImg from '@/components/cus-previewImg/cus-previewImg.vue'
	import {getProductCode} from '@/api/store.js'
	export default {
		components: {
			shareRedPackets,
			productConSwiper,
			"jyf-parser": parser,
			cusPreviewImg,
			"product-window": productWindow,
			userEvaluation,
			countDown
		},
		computed: mapGetters({
			'isLogin': 'isLogin',
			'userInfo': 'userInfo',
			'uid': 'uid',
			'chatUrl': 'chatUrl'
		}),
		data() {
			return {
				urlDomain: this.$Cache.get("imgHost"),
				showSkeleton: true, //骨架屏显示隐藏
				isNodes: 0, //控制什么时候开始抓取元素节点,只要数值改变就重新抓取
				bgColor:{
					'bgColor': '',
					'Color': '#999999',
					'isDay': true
				},
				userCollect:false,
				dataShow: 0,
				navH: '',
				id: 0,
				itemNew: [],
				indicatorDots: false,
				circular: true,
				autoplay: true,
				interval: 3000,
				duration: 500,
				attribute: {
					cartAttr: false,
					productAttr: [],
					productSelect: {}
				},
				productValue: [],
				isOpen: false,
				attr: '请选择',
				attrValue: '',
				AllIndex: 2,
				maxAllIndex: 0,
				replyChance: '',
				limitNum: 1,
				timeer: null,
				iSplus: false,
				navH: "",
				navList: ['商品', '评价', '详情'],
				opacity: 0,
				scrollY: 0,
				topArr: [],
				toView: '',
				height: 0,
				heightArr: [],
				lock: false,
				scrollTop: 0,
				storeInfo: {},
				pinkOkSum: 0,
				pink: [],
				replyCount: 0,
				reply: [],
				imgUrls: [],
				sharePacket: {
					isState: true, //默认不显示
				},
				tagStyle: {
					img: 'width:100%;display:block;',
					table: 'width:100%',
					video: 'width:100%'
				},
				posters: false,
				weixinStatus: false,
				posterImageStatus: false,
				canvasStatus: false, //海报绘图标签
				storeImage: '', //海报产品图
				PromotionCode: '', //二维码图片
				posterImage: '', //海报路径
				posterbackgd: `${this.$Cache.get("imgHost")}crmebimage/perset/staticImg/posterbackgd.png`,
				navActive: 0,
				actionSheetHidden: false,
				attrTxt: '',
				cart_num: '',
				isAuto: false, //没有授权的不会自动授权
				isShowAuth: false, //是否隐藏授权
				AllIndexDefault: 0,
				imgTop: '',
				qrcodeSize: 600,
				H5ShareBox: false, //公众号分享图片
				onceNum: 0, //一次可以购买几个
				errT: '',
				returnShow: true,
				homeTop: 20,
				navbarRight:0,
				theme:app.globalData.theme,
				skuArr:[],
				currentPage:false,
				selectSku:{},
				selectNavList:[
					{name:'首页',icon:'icon-shouye8',url:'/pages/index/index',after:'dialog_after'},
					{name:'搜索',icon:'icon-sousuo6',url:'/pages/goods/goods_search/index',after:'dialog_after'},
					{name:'购物车',icon:'icon-gouwuche7',url:'/pages/order_addcart/order_addcart',after:'dialog_after'},
					{name:'我的收藏',icon:'icon-shoucang3',url:'/pages/users/user_goods_collection/index',after:'dialog_after'},
					{name:'个人中心',icon:'icon-gerenzhongxin1',url:'/pages/user/index'},
				],
				chatConfig:{
					consumer_hotline:'',
					telephone_service_switch:'close',
					wx_chant_independent:'open'
				}, //客服配置
				masterStatus:'',
				openPages: '' ,//分享地址
				videoLink: '',
			}
		},
		watch: {
			isLogin: {
				handler: function(newV, oldV) {
					if (newV) {
						this.combinationDetail();
					}
				},
				deep: true
			},
		},
		onLoad(options) {
			let that = this
			 //用户从分享卡片进入的场景下获取主题色配置
			that.$set(that,'theme',that.$Cache.get('theme'));
			
			setTimeout(() => {
				that.isNodes++;
			}, 500);
			
			//重置商品类型
			this.$store.commit("PRODUCT_TYPE", 'normal');
			
			var pages = getCurrentPages();
			that.$set(that,'chatConfig',that.$Cache.getItem('chatConfig'));
			this.$nextTick(() => {
				// #ifdef MP
				const menuButton = uni.getMenuButtonBoundingClientRect();
				const query = uni.createSelectorQuery().in(this);
				query
					.select('#home')
					.boundingClientRect(data => {
						this.homeTop = menuButton.top * 2 + menuButton.height - data.height;
					})
					.exec();
				// #endif
			});
			
			// #ifdef MP
			this.navH = app.globalData.navHeight;
			// #endif
			// #ifndef MP
			this.navH = 96;
			// #endif
			
			//设置商品列表高度
			uni.getSystemInfo({
				success: function(res) {
					that.height = res.windowHeight
					//res.windowHeight:获取整个窗口高度为px，*2为rpx；98为头部占据的高度；
				},
			});
			
			//获取浏览器参数
			if(options.spread) app.globalData.spread = options.spread; //推广码
			if (options.id) this.id = options.id; // 商品id
			// 仅仅小程序扫码进入获取商品id，商品类型
			if (options.scene) {
				let qrCodeValue = this.$util.getUrlParams(decodeURIComponent(options.scene));
				let mapeMpQrCodeValue = this.$util.formatMpQrCodeData(qrCodeValue);
				this.id = mapeMpQrCodeValue.id ? mapeMpQrCodeValue.id : '';
			}
			this.combinationDetail();
			if(this.isLogin && parseInt(app.globalData.spread)>0){
				silenceBindingSpread()
			}
			
			// if (this.isLogin) {
			// 	this.combinationDetail();
			// 	if(parseInt(app.globalData.spread)>0) silenceBindingSpread()
			// } else {
			// 	this.$Cache.set('login_back_url',
			// 		`/pages/activity/goods_combination_details/index?id=${options.id}&spread=${app.globalData.spread?app.globalData.spread:0}`
			// 	);
			// 	toLogin();
			// }
		},
		methods: {
			wxChatService(){
				let chatUrlArr = this.chatUrl.split('?')
				uni.navigateTo({
					url:`/pages/users/web_page/index?webUel=${chatUrlArr[0]}&title=客服&${chatUrlArr[1]}`
				})
			},
			// app分享
			// #ifdef APP-PLUS
			appShare(scene) {
				let that = this
				let routes = getCurrentPages(); // 获取当前打开过的页面路由数组
				let curRoute = routes[routes.length - 1].$page.fullPath // 获取当前页面路由，也就是最后一个打开的页面路由
				uni.share({
					provider: "weixin",
					scene: scene,
					type: 0,
					href: `${HTTP_H5_URL}${curRoute}&spread=${that.uid}`,
					title: that.storeInfo.storeName,
					summary: app.globalData.companyName,
					imageUrl: that.storeInfo.image,
					success: function(res) {
						that.posters = false;
					},
					fail: function(err) {
						uni.showToast({
							title: '分享失败',
							icon: 'none',
							duration: 2000
						})
						that.posters = false;
					}
				});
			},
			// #endif

			getProductReplyCount: function() {
				let that = this;
				getReplyConfig(that.storeInfo.productId).then(res => {
					that.$set(that, 'replyChance', res.data.replyChance * 100);
					that.$set(that, 'replyCount', res.data.sumCount);
				});
			},
			getProductReplyList: function() {
				getReplyProduct(this.storeInfo.productId).then(res => {
					this.reply = res.data.productReply ? [res.data.productReply] : [];
				})
			},
			onClickService() {
				if(this.chatConfig.telephone_service_switch === 'open'){
					uni.makePhoneCall({
					    phoneNumber: this.chatConfig.consumer_hotline //仅为示例
					});
				}else{
					// #ifdef APP-PLUS
					uni.navigateTo({
						url: '/pages/users/web_page/index?webUel=' + this.chatUrl + '&title=客服'
					})
					// #endif
					// #ifndef APP-PLUS
					location.href = this.chatUrl;
					// #endif
				}
			},
			closePosters: function() {
				this.posters = false;
			},
			closeChange: function() {
				this.$set(this.sharePacket, 'isState', true);
			},
			showAll: function() {
				this.AllIndexDefault = this.AllIndex;
				this.AllIndex = this.pink.length;
			},
			hideAll: function() {
				this.AllIndex = this.AllIndexDefault;
			},
			// 授权关闭
			authColse: function(e) {
				this.isShowAuth = e;
			},
			/**
			 * 购物车手动填写
			 * 
			 */
			iptCartNum: function(e) {
				if (e > this.onceNum) {
					this.$util.Tips({
						title: `该商品每次限购${this.onceNum}${this.storeInfo.unitName}`
					});
					this.$set(this.attribute.productSelect, 'cart_num', this.onceNum);
					this.$set(this, "cart_num", this.onceNum);
				} else {
					this.$set(this.attribute.productSelect, 'cart_num', e);
					this.$set(this, "cart_num", e);
				}
			},
			// 返回
			returns() {
				uni.navigateBack();
			},
			showNav(){
				this.currentPage = !this.currentPage;
			},
			// 获取详情
			combinationDetail() {
				var that = this;
				var data = that.id;
				getCombinationDetail(data).then(function(res) {
					//分享地址
					that.openPages = '/pages/activity/goods_combination_details/index?id=' + that.id + '&spread=' + that.uid;
					that.dataShow = 1;
					uni.setNavigationBarTitle({
						title: res.data.storeCombination.storeName.substring(0, 16)
					})
					that.masterStatus = res.data.masterStatus;
					that.storeInfo = res.data.storeCombination;
					that.getProductReplyList();
					that.getProductReplyCount();
					let sliderImage = JSON.parse(res.data.storeCombination.sliderImage);
					if (that.getFileType(sliderImage[0]) == 'video') {
						//判断轮播图第一张是否是视频，如果是，就赋值给videoLink，并且将其在轮播图中删除
						that.$set(that, 'videoLink', sliderImage[0]);
						sliderImage.splice(0, 1);
					}
					that.imgUrls = sliderImage || [];
					that.attribute.productSelect.num = res.data.storeCombination.onceNum;
					that.userCollect = res.data.userCollect;
					that.pink = res.data.pinkList || [];
					// that.pindAll = res.data.pindAll || [];
					that.itemNew = res.data.pinkOkList || [];
					that.pinkOkSum = res.data.pinkOkSum;
					that.attribute.productAttr = res.data.productAttr || [];
					that.productValue = res.data.productValue;
					for(let key in res.data.productValue){
						let obj = res.data.productValue[key];
						that.skuArr.push(obj)
					}
					that.$set(that, "selectSku", that.skuArr[0]);
					that.onceNum = res.data.storeCombination.onceNum;
					//	that.PromotionCode = res.data.storeInfo.code_base
					// #ifdef H5
					that.setShare();
					that.storeImage = that.storeInfo.image;
					that.getImageBase64(that.storeInfo.image);
					that.make();
					// #endif
					// #ifdef APP-PLUS
					that.downloadFilestoreImage();
					that.downloadFileAppCode();
					// #endif
					// #ifdef MP
					that.getQrcode();
					that.imgTop = res.data.storeCombination.image;
					// #endif
					// #ifndef H5
					that.downloadFilestoreImage();
					// #endif
					// that.setProductSelect();
					let productAttr = res.data.productAttr.map(item => {
					return {
						attrName : item.attrName,
						attrValues: item.attrValues.split(','),
						id:item.id,
						isDel:item.isDel,
						productId:item.productId,
						type:item.type
					 }
					});
					that.$set(that.attribute,'productAttr',productAttr);
					that.DefaultSelect();
					setTimeout(function() {
						that.infoScroll();
					}, 500);
					setTimeout(() => {
						that.showSkeleton = false
					}, 1000)

				}).catch(function(err) {
					that.$util.Tips({
						title: err
					}, {
						tab: 3
					})
				})
			},
			//#ifdef H5
			setShare: function() {
				this.$wechat.isWeixin() &&
					this.$wechat.wechatEvevt([
						"updateAppMessageShareData",
						"updateTimelineShareData",
						"onMenuShareAppMessage",
						"onMenuShareTimeline"
					], {
						desc: app.globalData.companyName,
						title: this.storeInfo.storeName,
						link: location.href,
						imgUrl: this.storeInfo.image
					}).then(res => {
						
					}).catch(err => {
						console.log(err);
					});
			},
			//#endif
			/**
			 * 默认选中属性
			 * 
			 */
			DefaultSelect: function() {
				let self = this, productAttr = self.attribute.productAttr,value = [];
				for (var key in self.productValue) {
					if (self.productValue[key].quota > 0) {
						value = productAttr.length ? key.split(",") : [];
						break;
					}
				}
				for (let i = 0; i < value.length; i++) {
					self.$set(productAttr[i], "index", value[i]);
				}
				//sort();排序函数:数字-英文-汉字；
				let productSelect = self.productValue[value.join(",")];
				if (productSelect && productAttr.length) {
					self.$set(self.attribute.productSelect,"storeName",self.storeInfo.storeName);
					self.$set(self.attribute.productSelect, "image", productSelect.image);
					self.$set(self.attribute.productSelect, "price", productSelect.price);
					self.$set(self.attribute.productSelect, "unique", productSelect.id);
					self.$set(self.attribute.productSelect, "quota", productSelect.quota);
					self.$set(self.attribute.productSelect, "quotaShow", productSelect.quotaShow);
					self.$set(self.attribute.productSelect, "cart_num", 1);
					this.$set(this, "attrValue", value.join(","));
					this.$set(this, "attrTxt", "已选择");
				} else if (!productSelect && productAttr.length) {
					self.$set(self.attribute.productSelect,"storeName",self.storeInfo.storeName);
					self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
					self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
					self.$set(self.attribute.productSelect, "quota", 0);
					self.$set(self.attribute.productSelect, "quotaShow", 0);
					self.$set(self.attribute.productSelect, "unique", "");
					self.$set(self.attribute.productSelect, "cart_num", 0);
					self.$set(self, "attrValue", "");
					self.$set(self, "attrTxt", "请选择");
				} else if (!productSelect && !productAttr.length) {
					self.$set(self.attribute.productSelect,"storeName",self.storeInfo.storeName);
					self.$set(self.attribute.productSelect, "image", self.storeInfo.image);
					self.$set(self.attribute.productSelect, "price", self.storeInfo.price);
					self.$set(self.attribute.productSelect, "quota", self.storeInfo.quota);
					let uniId = self.skuArr[0].id;
					self.$set(self.attribute.productSelect,"unique", uniId);
					self.$set(self.attribute.productSelect, "cart_num", 1);
					self.$set(self, "attrValue", "");
					self.$set(self, "attrTxt", "请选择");
				}
			},

			infoScroll: function() {
				var that = this,
					topArr = [],
					heightArr = [];
				for (var i = 0; i < that.navList.length; i++) { //productList
					//获取元素所在位置
					var query = uni.createSelectorQuery().in(this);
					var idView = "#past" + i;
					// if (!that.data.good_list.length && i == 2) {
					//   var idView = "#past" + 3;
					// }
					query.select(idView).boundingClientRect();
					query.exec(function(res) {
						var top = res[0].top;
						var height = res[0].height;
						topArr.push(top);
						heightArr.push(height);
						that.topArr = topArr
						that.heightArr = heightArr
					});
				};
			},
			selecAttr: function() {
				this.attribute.cartAttr = true
			},
			onMyEvent: function() {
				this.$set(this.attribute, 'cartAttr', false);
				this.$set(this, 'isOpen', false);
			},
			/**
			 * 购物车数量加和数量减
			 * 
			 */
			ChangeCartNum: function(changeValue) {
				//changeValue:是否 加|减
				//获取当前变动属性
				let productSelect = this.productValue[this.attrValue];
				if (this.buyNum === productSelect.quota) {
					return this.$util.Tips({
						title: '您已超出当前商品每人限购数量，请浏览其他商品'
					});
				}
				if (this.cart_num) {
					productSelect.cart_num = this.cart_num;
					this.attribute.productSelect.cart_num = this.cart_num;
				}
				//如果没有属性,赋值给商品默认库存
				if (productSelect === undefined && !this.attribute.productAttr.length) productSelect = this.attribute
					.productSelect;
				//无属性值即库存为0；不存在加减；
				if (productSelect === undefined) return;
				let quotaShow = productSelect.quota_show || 0;
				let quota = productSelect.quota || 0;
				let num = this.attribute.productSelect;
				let nums = this.storeInfo.onceNum || 0;
				//设置默认数据
				if (productSelect.cart_num == undefined) productSelect.cart_num = 1;
				if (changeValue) {
					if (num.cart_num === this.onceNum) {
						return this.$util.Tips({
							title: `该商品每次限购${this.onceNum}${this.storeInfo.unitName}`
						});
					}
					num.cart_num++;
					let arrMin = [];
					arrMin.push(nums);
					arrMin.push(quota);
					// arrMin.push(stock);
					let minN = Math.min.apply(null, arrMin);
					if (num.cart_num >= minN) {
						this.$set(this.attribute.productSelect, "cart_num", minN ? minN : 1);
						this.$set(this, "cart_num", minN ? minN : 1);
					}
					this.$set(this, "cart_num", num.cart_num);
					this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
				} else {
					num.cart_num--;
					if (num.cart_num < 1) {
						this.$set(this.attribute.productSelect, "cart_num", 1);
						this.$set(this, "cart_num", 1);
					}
					this.$set(this, "cart_num", num.cart_num);
					this.$set(this.attribute.productSelect, "cart_num", num.cart_num);
				}
			},
			attrVal(val) {
				this.attribute.productAttr[val.indexw].index = this.attribute.productAttr[val.indexw].attrValues[val
					.indexn];
			},
			/**
			 * 属性变动赋值
			 * 
			 */
			ChangeAttr: function(res) {
				this.$set(this, 'cart_num', 1);
				let productSelect = this.productValue[res];
				this.$set(this, "selectSku", productSelect);
				if (productSelect) {
					this.$set(this.attribute.productSelect, "image", productSelect.image);
					this.$set(this.attribute.productSelect, "price", productSelect.price);
					this.$set(this.attribute.productSelect, "unique", productSelect.id);
					this.$set(this.attribute.productSelect, "cart_num", 1);
					this.$set(this.attribute.productSelect, "quota", productSelect.quota);
					this.$set(this.attribute.productSelect, "quotaShow", productSelect.quotaShow);
					this.$set(this, "attrValue", res);

					this.attrTxt = "已选择"
				} else {
					this.$set(this.attribute.productSelect, "image", this.storeInfo.image);
					this.$set(this.attribute.productSelect, "price", this.storeInfo.price);
					this.$set(this.attribute.productSelect, "unique", "");
					this.$set(this.attribute.productSelect, "cart_num", 0);
					this.$set(this.attribute.productSelect, "quota", 0);
					this.$set(this.attribute.productSelect, "quotaShow", 0);
					this.$set(this, "attrValue", "");
					this.attrTxt = "已选择"
				}
			},
			// 单独购买
			goProduct() {
				uni.navigateTo({
					url: '/pages/goods/goods_details/index?id=' + this.storeInfo.productId
				})
			},
			// 立即购买
			goCat() {
				var that = this;
				var productSelect = this.productValue[this.attrValue];
				//打开属性
				if (this.isOpen)
					this.attribute.cartAttr = true
				else
					this.attribute.cartAttr = !this.attribute.cartAttr
				//只有关闭属性弹窗时进行加入购物车
				if (this.attribute.cartAttr === true && this.isOpen == false) return this.isOpen = true
				//如果有属性,没有选择,提示用户选择
				if (this.attribute.productAttr.length && productSelect === undefined && this.isOpen == true) return that
					.$util.Tips({
						title: '请选择属性'
					});
				var data = {
					productId: that.storeInfo.productId,
					combinationId: parseFloat(that.id),
					cartNum: that.cart_num ? this.cart_num : this.attribute.productSelect.cart_num,
					productAttrUnique: productSelect !== undefined ? productSelect.id : '',
					isNew: true,
				};
				this.$Order.getPreOrder("buyNow",[{
						"attrValueId": parseFloat(this.attribute.productSelect.unique),
						"combinationId": parseFloat(this.id),
						"productNum": parseFloat(this.cart_num ? this.cart_num : this.attribute.productSelect.cart_num),
						"productId": parseFloat(this.storeInfo.productId)
					}]);
			},
			/**
			 * 收藏商品
			 */
			setCollect: function() {
				var that = this;
				if (this.userCollect) {
					collectDel(this.storeInfo.productId).then(res => {
						that.userCollect = !that.userCollect
					})
				} else {
					collectAdd(this.storeInfo.productId).then(res => {
						that.userCollect = !that.userCollect
					})
				}
			},

			/**
			 * 分享打开
			 * 
			 */
			listenerActionSheet: function() {
				if (this.isLogin == false) {
					toLogin();
				} else {
					// #ifdef H5
					if (this.$wechat.isWeixin() === true) {
						this.weixinStatus = true;
					}
					// #endif
					// #ifndef APP-PLUS
					this.goPoster()
					// #endif
					this.posters = true;
				}
			},
			// 分享关闭
			listenerActionClose: function() {
				this.canvasStatus = false;
			},
			//隐藏海报
			posterImageClose: function() {
				this.canvasStatus = false;
				this.posters = false;
			},
			//替换安全域名
			setDomain: function(url) {
				url = url ? url.toString() : '';
				//本地调试打开,生产请注销
				if (url.indexOf("https://") > -1) return url;
				else return url.replace('http://', 'https://');
			},
			//获取海报产品图
			downloadFilestoreImage: function() {
				let that = this;
				uni.downloadFile({
					url: that.setDomain(that.storeInfo.image),
					success: function(res) {
						that.storeImage = res.tempFilePath;
					},
					fail: function() {
						return that.$util.Tips({
							title: ''
						});
						that.storeImage = '';
					},
				});
			},
			getFileType(fileName) {
				// 后缀获取
				let suffix = '';
				// 获取类型结果
				let result = '';
				try {
					const flieArr = fileName.split('.');
					suffix = flieArr[flieArr.length - 1];
				} catch (err) {
					suffix = '';
				}
				// fileName无后缀返回 false
				if (!suffix) {
					return false;
				}
				suffix = suffix.toLocaleLowerCase();
				// 图片格式
				const imglist = ['png', 'jpg', 'jpeg', 'bmp', 'gif'];
				// 进行图片匹配
				result = imglist.find(item => item === suffix);
				if (result) {
					return 'image';
				}
				// 匹配 视频
				const videolist = ['mp4', 'm2v', 'mkv', 'rmvb', 'wmv', 'avi', 'flv', 'mov', 'm4v'];
				result = videolist.find(item => item === suffix);
				if (result) {
					return 'video';
				}
				// 其他 文件类型
				return 'other';
			},
			// app获取二维码
			downloadFileAppCode() {
				let that = this;
				uni.downloadFile({
					url: that.setDomain(that.storeInfo.code_base),
					success: function(res) {
						that.PromotionCode = res.tempFilePath;
					},
					fail: function() {
						return that.$util.Tips({
							title: ''
						});
						that.PromotionCode = '';
					},
				});
			},

			getImageBase64: function(images) {
				let that = this;
				imageBase64({
					url: images
				}).then(res => {
					that.imgTop = res.data.code
				})
			},
			// 小程序关闭分享弹窗；
			goFriend: function() {
				this.posters = false;
			},
			/**
			 * 生成海报
			 */
			goPoster: function() {
				let that = this;
				uni.showLoading({
					title: '海报生成中',
					mask: true
				});
				that.posters = false;
				let arrImagesUrl = '';
				let arrImagesUrlTop = '';
				if (!that.PromotionCode) {
					uni.hideLoading();
					that.$util.Tips({
						title: that.errT
					});
					return
				}
				uni.downloadFile({
					url: that.imgTop,
					success: (res) => {
						arrImagesUrlTop = res.tempFilePath;
						let arrImages = [that.posterbackgd, arrImagesUrlTop, that.PromotionCode];
						let storeName = that.storeInfo.storeName;
						let price = that.storeInfo.price;
						setTimeout(() => {
							that.$util.PosterCanvas(arrImages, storeName, price, that.storeInfo
								.otPrice,
								function(tempFilePath) {
									that.posterImage = tempFilePath;
									that.canvasStatus = true;
									uni.hideLoading();
								});
						}, 500);
					}
				});
			},
			// 图片预览；
			getpreviewImage: function() {
				if (this.posterImage) {
					let photoList = [];
					photoList.push(this.posterImage)
					uni.previewImage({
						urls: photoList,
						current: this.posterImage
					});
				} else {
					this.$util.Tips({
						title: '您的海报尚未生成'
					});
				}
			},
			// 小程序二维码
			getQrcode() {
				let that = this;
				let data = {
					pid: that.uid,
					id: that.id,
					path: 'pages/activity/goods_combination_details/index'
				}
				getQrcode(data).then(res => {
					base64src(res.data.code,Date.now(), res => {
						that.PromotionCode = res;
					});
				}).catch(err => {
					that.errT = err;
				});
			},
			// 生成二维码；
			make() {
				let href = location.href.split('?')[0] + "?id="+ this.id + "&spread="  + this.uid;
				uQRCode.make({
					canvasId: 'qrcode',
					text: href,
					size: this.qrcodeSize,
					margin: 10,
					success: res => {
						this.PromotionCode = res;

					},
					complete: (res) => {},
					fail: res => {
						this.$util.Tips({
							title: '海报二维码生成失败！'
						});
					}
				})
			},
			/*
			 * 保存到手机相册
			 */
			// #ifdef MP
			savePosterPath: function() {
				let that = this;
				uni.getSetting({
					success(res) {
						if (!res.authSetting['scope.writePhotosAlbum']) {
							uni.authorize({
								scope: 'scope.writePhotosAlbum',
								success() {
									uni.saveImageToPhotosAlbum({
										filePath: that.posterImage,
										success: function(res) {
											that.posterImageClose();
											that.$util.Tips({
												title: '保存成功',
												icon: 'success'
											});
										},
										fail: function(res) {
											that.$util.Tips({
												title: '保存失败'
											});
										}
									})
								}
							})
						} else {
							uni.saveImageToPhotosAlbum({
								filePath: that.posterImage,
								success: function(res) {
									that.posterImageClose();
									that.$util.Tips({
										title: '保存成功',
										icon: 'success'
									});
								},
								fail: function(res) {
									that.$util.Tips({
										title: '保存失败'
									});
								},
							})
						}
					}
				})
			},
			// #endif
			setShareInfoStatus: function() {
				let data = this.storeInfo;
				let href = location.href;
				if (this.$wechat.isWeixin()) {
					href =
						href.indexOf("?") === -1 ?
						href + "?spread=" + this.uid :
						href + "&spread=" + this.uid;

					let configAppMessage = {
						desc: data.storeInfo,
						title: data.storeName,
						link: href,
						imgUrl: data.image
					};
					this.$wechat.wechatEvevt(["updateAppMessageShareData", "updateTimelineShareData"],
						configAppMessage)
				}
			},
			scroll: function(e) {
				var that = this,
					scrollY = e.detail.scrollTop;
				var opacity = scrollY / 500;
				opacity = opacity > 1 ? 1 : opacity;
				that.opacity = opacity
				that.scrollY = scrollY
				if (that.lock) {
					that.lock = false
					return;
				}
				for (var i = 0; i < that.topArr.length; i++) {
					if (scrollY < that.topArr[i] - (app.globalData.navHeight / 2) + that.heightArr[i]) {
						that.navActive = i
						break
					}
				}
			},
			tap: function(item, index) {
				var id = item.id;
				var index = index;
				var that = this;
				// if (!this.data.good_list.length && id == "past2") {
				//   id = "past3"
				// }
				this.toView = id;
				this.navActive = index;
				this.lock = true;
				this.scrollTop = index > 0 ? that.topArr[index] - (app.globalData.navHeight / 2) : that.topArr[index]
			},
			hideNav(){
				this.currentPage = false;
			},
			//下拉导航页面跳转
			linkPage(url){
				if(url == '/pages/index/index' || url == '/pages/order_addcart/order_addcart' || url == '/pages/user/index'){
					uni.switchTab({
						url
					})
				}else{
					uni.navigateTo({
						url
					})
				}
				this.currentPage = false
			},
			showImg(index){
				this.$refs.cusPreviewImg.open(this.selectSku.suk)
			},
			changeSwitch(e){
				let productSelect = this.skuArr[e];
				this.$set(this,'selectSku',productSelect);
				var skuList = productSelect.suk.split(',');
				skuList.forEach((i,index)=>{
					this.$set(this.attribute.productAttr[index],'index',skuList[index]);
				})
				if (productSelect) {
					this.$set(this.attribute.productSelect, "image", productSelect.image);
					this.$set(this.attribute.productSelect, "price", productSelect.price);
					this.$set(this.attribute.productSelect, "stock", productSelect.stock);
					this.$set(this.attribute.productSelect, "unique", productSelect.id);
				    this.$set(this.attribute.productSelect, "quota", productSelect.quota);
					this.$set(this.attribute.productSelect, "quotaShow", productSelect.quotaShow);
					this.$set(this.attribute.productSelect, "cart_num", 1);
					this.$set(this, "attrTxt", "已选择")
					this.$set(this, "attrValue", productSelect.suk)
				}
			}
		},
		//#ifdef MP
		onShareAppMessage() {
			let that = this;
			return {
				title: that.storeInfo.storeName,
				path: that.openPages,
				imageUrl: that.storeInfo.image
			};
		}
		//#endif

	}
</script>

<style scoped lang="scss">
	.pl-14{
		padding-left: 14rpx;
	}
	.mb-02{
		margin-bottom: 4rpx;
	}
	.select_nav{
		width: 170rpx !important;
		height: 60rpx !important;
		border-radius: 33rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0,0,0,0.07);
		color: #000;
		position: fixed;
		font-size: 18px;
		line-height: 58rpx;
		z-index: 1000;
		left: 14rpx;
	}
	.px-20{
		padding: 0 20rpx 0;
	}
	.justify-center{
		justify-content: center;
	}
	.align-center {
		align-items: center;
	}
	.nav_line{
		content: '';
		display: inline-block;
		width: 1px;
		height: 34rpx;
		background: rgba(0, 0, 0, 0.2);
		position: absolute;
		left: 0;
		right: 0;
		margin: auto;
	}
	.bgwhite{
		background: #fff;
	}
	.input {
		display: flex;
		align-items: center;
		/* #ifdef MP */
		width: 300rpx;
		/* #endif */
		/* #ifndef MP */
		width: 460rpx;
		/* #endif */
		height: 58rpx;
		padding: 0 0 0 30rpx;
		border: 1px solid #E0E0E0;
		border-radius: 29rpx;
		color: #666;
		font-size: 26rpx;
		position: fixed;
		left: 0;
		right: 0;
		margin: auto;
		background: rgba(255, 255, 255, 0.3);
		.iconfont {
			margin-right: 20rpx;
			font-size: 26rpx;
			color: #666666;
		}
	}
	.container_detail{
		/* #ifdef MP */
		margin-top:32rpx;
		/* #endif */
	}
	.tab_nav{
		width: 100%;
		height: 48px;
		padding:0 30rpx 0;
	}
	.right_select{
		width: 58rpx;
		height: 58rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0,0,0,0.1);
		border-radius: 50%;
		position: fixed;
		right: 20rpx;
		text-align: center;
		line-height: 58rpx;
	}
	.dialog_nav{
		position: absolute;
		/* #ifdef MP */
		left: 14rpx;
		/* #endif */
		/* #ifdef H5 || APP-PLUS*/
		right: 14rpx;
		/* #endif */
		width: 240rpx;
		background: #FFFFFF;
		box-shadow: 0px 0px 16rpx rgba(0, 0, 0, 0.08);
		z-index: 310;
		border-radius: 14rpx;
		&::before{
			content: '';
			width: 0;
			height: 0;
			position: absolute;
			/* #ifdef MP */
			left: 0;
			right: 0;
			margin:auto;
			/* #endif */
			/* #ifdef H5 || APP-PLUS */
			right: 8px;
			/* #endif */
			top:-9px;
			border-bottom: 10px solid #F5F5F5;
			border-left: 10px solid transparent;    /*transparent 表示透明*/
			border-right: 10px solid transparent;
		}
	}
	.dialog_nav_item{
		width: 100%;
		height: 84rpx;
		line-height: 84rpx;
		padding: 0 20rpx 0;
		box-sizing: border-box;
		border-bottom: #eee;
		font-size: 28rpx;
		color: #333;
		position: relative;
		.iconfont{
			font-size: 32rpx;
		}
	}
	.dialog_after{
		::after{
			content: '';
			position: absolute;
			width:86px;
			height: 1px;
			background-color: #EEEEEE;
			bottom: 0;
			right: 0;
		}
	}
	.pl-20{
		padding-left: 20rpx;
	}
	.userEvaluation{
		i{
			display: inline-block;
		}
	}
	.attribute{
		.line1{
			width: 600rpx;
		}
	}
	.share-box {
		z-index: 1000;
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.generate-posters {
		width: 100%;
		height: 318rpx;
		background-color: #fff;
		position: fixed;
		left: 0;
		bottom: 0;
		z-index: 388;
		transform: translate3d(0, 100%, 0);
		transition: all 0.3s cubic-bezier(0.25, 0.5, 0.5, 0.9);
		border-top: 1rpx solid #eee;
		
		.generateCon {
			height: 220rpx;
		}
		
		.generateClose {
			height: 98rpx;
			font-size: 28rpx;
			color: #333333;
			border-top: 1px solid #eee;
		}
		
		.item {
			.pictrue {
				width: 96rpx;
				height: 96rpx;
				border-radius: 50%;
				margin: 0 auto 6rpx auto;
		
				image {
					width: 100%;
					height: 100%;
					border-radius: 50%;
				}
			}
		}
	}
	
	.generate-posters.on {
		transform: translate3d(0, 0, 0);
	}
	
	.generate-posters .item {
		flex: 1;
		text-align: center;
		font-size: 30rpx;
	}
	
	.generate-posters .item .iconfont {
		font-size: 80rpx;
		color: #5eae72;
	}
	
	.generate-posters .item .iconfont.icon-haibao {
		color: #5391f1;
	}
	
	.generate-posters .item .iconfont.icon-haowuquan1 {
		color: #ff954d;
	}
	
	.navbar .header {
		font-size: 30rpx;
		color: #050505;
		display: flex;
		align-items: center;
		/* #ifndef MP */
		width: 460rpx;
		/* #endif */
		/* #ifdef MP */
		width: 360rpx;
		/* #endif */
		height: 100rpx;
		position: fixed;
		left: 0;
		right: 0;
		margin: auto;
	}

	.icon-xiangzuo {
		/* #ifdef H5 */
		top: 20rpx !important;
		/* #endif */
	}

	.navbar .header .item {
		position: relative;
		margin: 0 25rpx;
	}

	.navbar .header .item.on:before {
		position: absolute;
		width: 60rpx;
		height: 5rpx;
		background-repeat: no-repeat;
		content: "";
		@include linear-gradient(theme);
		bottom: -10rpx;
		left: 50%;
		margin-left: -28rpx;
	}

	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		z-index: 98;
		width: 100%;
	}

	.navbar .navbarH {
		position: relative;
	}

	.navbar .navbarH .navbarCon {
		position: absolute;
		bottom: 0;
		height: 100rpx;
		width: 100%;
	}

	.h5_back {
		color: #000;
		position: fixed;
		left:20rpx;
		font-size: 32rpx;
		text-align: center;
		width: 58rpx;
		height: 58rpx;
		background: rgba(255, 255, 255, 0.3);
		border: 1px solid rgba(0,0,0,0.1);
		border-radius: 50%;
	}

	.product-con .wrapper {
        margin-top: 30rpx;
		border-radius: 14rpx;
	}

	.product-con .wrapper .share .money .y-money {
		color: #82848f;
		margin-left: 13rpx;
		text-decoration: line-through;
		font-weight: normal;
	}
	.font_color{
		@include main_color(theme);
	}
	.product-con .notice {
		width: 100%;
		height: 62rpx;
		@include coupons_light_color(theme);
		padding: 0 24rpx;
		box-sizing: border-box;
	}

	.product-con .notice .num {
		font-size: 24rpx;
		@include main_color(theme);
	}

	.product-con .notice .num .iconfont {
		font-size: 30rpx;
		vertical-align: -3rpx;
		margin-right: 20rpx;
	}

	.product-con .notice .num .line {
		color: #333333;
		margin-left: 15rpx;
	}

	.product-con .notice .swiper {
		height: 100%;
		width: 360rpx;
		line-height: 62rpx;
		overflow: hidden;
		margin-left: 14rpx;
	}

	.product-con .notice .swiper swiper {
		height: 100%;
		width: 100%;
		overflow: hidden;
		font-size: 24rpx;
		color: #333333;
	}

	.product-con .assemble {
		background-color: #fff;
	}

	.product-con .assemble .item {
		padding-right: 24rpx;
		margin-left: 24rpx;
		border-bottom: 1rpx solid #f0f0f0;
		height: 130rpx;
	}

	.product-con .assemble .item .pictxt .text {
		width: 172rpx;
		margin-left: 16rpx;
	}

	.product-con .assemble .item .pictxt .pictrue {
		width: 80rpx;
		height: 80rpx;
		background: #f0f0f0;
		border-radius: 50%;
	}

	.product-con .assemble .item .pictxt .pictrue image {
		width: 100%;
		height: 100%;
		border-radius: 50%;
	}

	.product-con .assemble .item .right .lack {
		font-size: 24rpx;
		color: #333333;
	}

	.product-con .assemble .item .right .time {
		position: relative;
		left: -10rpx;
		font-size: 22rpx;
		color: #82848f;
		margin-top: 5rpx;
	}

	.product-con .assemble .item .right .spellBnt {
		font-size: 24rpx;
		color: #fff;
		width: 140rpx;
		height: 50rpx;
		border-radius: 50rpx;
		@include main_bg_color(theme);
		text-align: center;
		line-height: 50rpx;
		margin-left: 16rpx;
	}

	.product-con .assemble .item .right .spellBnt .iconfont {
		font-size: 25rpx;
		margin-left: 5rpx;
	}

	.product-con .assemble .more {
		font-size: 24rpx;
		color: #333333;
		text-align: center;
		height: 90rpx;
		line-height: 90rpx;
	}

	.product-con .assemble .more .iconfont {
		margin-left: 13rpx;
		font-size: 25rpx;
	}

	.product-con .playWay {
		background-color: #fff;
		padding: 0 24rpx;
		margin-top: 20rpx;
		font-size: 28rpx;
		color: #333333;
	}

	.product-con .playWay .title {
		height: 86rpx;
		border-bottom: 1rpx solid #eee;
		font-size: 28rpx;
	}

	.product-con .playWay .title .iconfont {
		margin-left: 13rpx;
		font-size: 26rpx;
		color: #717171;
	}

	.product-con .playWay .way {
		height: 110rpx;
		line-height: 110rpx;
		font-size: 26rpx;
		color: #333333;
	}

	.product-con .playWay .way .iconfont {
		color: #cdcdcd;
		font-size: 26rpx;
		margin: 0 35rpx;
	}

	.product-con .playWay .way .item .num {
		font-size: 35rpx;
		// vertical-align: 4rpx;
		margin-right: 6rpx;
		display: inline-block;
	}

	.product-con .playWay .way .item .tip {
		font-size: 22rpx;
		color: #a5a5a5;
		margin-top: 7rpx;
	}

	.product-con .footer {
		padding: 0 20rpx 0 30rpx;
		position: fixed;
		bottom:env(safe-area-inset-bottom);
		width: 100%;
		box-sizing: border-box;
		height: 100rpx;
		background-color: #fff;
		z-index: 277;
		border-top: 1rpx solid #f0f0f0;
		text-align: center;
		&:after{
			content:'';
			height:env(safe-area-inset-bottom); // 这里是重点
			position: absolute;
			top:100%;
			left: 0;
			right:0;
			background-color: #fff;
		}
	}

	.product-con .footer .item {
		font-size: 18rpx;
		color: #666;
	}

	.product-con .footer .item .iconfont {
		text-align: center;
		font-size: 40rpx;
	}

	.product-con .footer .item .iconfont.icon-shoucang1 {
		@include main_color(theme);
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 {
		font-size: 40rpx;
		position: relative;
	}

	.product-con .footer .item .iconfont.icon-gouwuche1 .num {
		color: #fff;
		position: absolute;
		font-size: 18rpx;
		padding: 2rpx 8rpx 3rpx;
		border-radius: 200rpx;
		top: -10rpx;
		right: -10rpx;
	}

	.product-con .footer .bnt {
		width: 540rpx;
		height: 76rpx;
	}

	.product-con .footer .bnt .bnts {
		width: 270rpx;
		text-align: center;
		line-height: 76rpx;
		color: #fff;
		font-size: 28rpx;
	}

	.product-con .footer .bnt .joinCart {
		border-radius: 50rpx 0 0 50rpx;
		@include left_color(theme);
	}
	.radius{
		border-radius: 50rpx 0 0 50rpx;
	}
	.product-con .footer .bnt .buy {
		border-radius: 0 50rpx 50rpx 0;
		@include main_bg_color(theme);
	}
	.buy-btns{
		border-radius: 0 50rpx 50rpx 0 !important;
	}
	.setCollectBox {
		font-size: 18rpx;
		color: #666;
	}

	.canvas {
		position: fixed;
		z-index: -5;
		opacity: 0;
	}

	.poster-pop {
		width: 450rpx;
		height: 714rpx;
		position: fixed;
		left: 50%;
		transform: translateX(-50%);
		z-index: 99;
		top: 50%;
		margin-top: -357rpx;
	}

	.poster-pop image {
		width: 100%;
		height: 100%;
		display: block;
	}

	.poster-pop .close {
		width: 46rpx;
		height: 75rpx;
		position: fixed;
		right: 0;
		top: -73rpx;
		display: block;
	}

	.poster-pop .save-poster {
		background-color: #df2d0a;
		font-size: ：22rpx;
		color: #fff;
		text-align: center;
		height: 76rpx;
		line-height: 76rpx;
		width: 100%;
	}

	.poster-pop .keep {
		color: #fff;
		text-align: center;
		font-size: 25rpx;
		margin-top: 10rpx;
	}

	.mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-color: rgba(0, 0, 0, 0.6);
		z-index: 9;
	}

	.pro-wrapper .iconn {
		background-image: url('data:image/png;base64,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');
		width: 100rpx;
		height: 100rpx;
		background-repeat: no-repeat;
		background-size: 100% 100%;
		margin: 0 auto;
	}

	.pro-wrapper .iconn.iconn1 {
		background-image: url('data:image/png;base64,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');
	}

	.home-nav {
		/* #ifdef H5 */
		top: 20rpx !important;
		/* #endif */
	}

	.home-nav {
		color: #fff;
		position: fixed;
		font-size: 33rpx;
		width: 56rpx;
		height: 56rpx;
		z-index: 999;
		left: 33rpx;
		background: rgba(190, 190, 190, 0.5);
		border-radius: 50%;

		&.on {
			background: unset;
			color: #333;
		}
	}

	.home-nav .line {
		width: 1rpx;
		height: 24rpx;
		background: rgba(255, 255, 255, 0.25);
	}

	.home-nav .icon-xiangzuo {
		width: auto;
		font-size: 28rpx;
	}

	.share-box {
		z-index: 1000;
		position: fixed;
		left: 0;
		top: 0;
		width: 100%;
		height: 100%;
	}

	.share-box image {
		width: 100%;
		height: 100%;
	}
	.mask_transparent{
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background:transparent;
		z-index: 300;
	}
	.attrImg{
		width: 66rpx;
		height: 66rpx;
		border-radius: 6rpx;
		display: block;
		margin-right: 14rpx;
	}
	.switchTxt{
		height: 60rpx;
		flex: 1;
		line-height: 60rpx;
		box-sizing: border-box;
		background: #EEEEEE;
		padding-right: 0 24rpx 0;
		border-radius: 8rpx;
		text-align: center;
	}
</style>
