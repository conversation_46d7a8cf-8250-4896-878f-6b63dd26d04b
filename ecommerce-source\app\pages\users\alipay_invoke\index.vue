<template>
	<view>
		<!-- #ifdef H5 -->
		<view v-if="!this.$wechat.isWeixin()">
			<view class="text-section">
				<view>{{ content }}</view>
			</view>
			<view v-html="formContent"></view>
		</view>
		<!-- #endif -->
		<view v-if="this.$wechat.isWeixin()">
			<view class="text-section">
				<view>点击复制网址去浏览器中打开</view>
				<view class="link">{{ link }}</view>
			</view>
			<view class="button-section">
				<!-- #ifdef H5 -->
				<button class="button copy" :data-clipboard-text="link">点击复制</button>
				<!-- #endif -->
				<!-- #ifdef MP -->
				<button class="button copy" @click="copyLink">点击复制</button>
				<!-- #endif -->
				<button class="button off" @click="goDetail">完成支付</button>
			</view>
		</view>
		<!-- #ifdef H5 -->
		<view v-show="hintShow" class="hint" @click="hintShow = false">
			<view>点击右上角<text class="iconfont icon-gengduo"></text></view>
			<view>选择 在浏览器 打开，去支付</view>
		</view>
		<!-- #endif -->
	</view>
</template>

<script>
	// #ifdef H5
	import ClipboardJS from '@/plugin/clipboard/clipboard.js';
	import {mapGetters} from 'vuex';
	import {toLogin} from '@/libs/login.js';
	import { orderPay } from '@/api/order.js';
	export default {
		data() {
			return {
				// #ifdef H5
				isWeixin: this.$wechat.isWeixin(),
				hintShow: true,
				// #endif
				orderId: '',
				link: '',
				pay_key: '',
				content: '正在支付中',
				formContent: ''
			};
		},
		computed: mapGetters(['isLogin']),
		onLoad(option) {
			if (!this.isLogin && this.$wechat.isWeixin()) {
				toLogin();
			}
			this.orderId = option.id;
			let rechar_id = option.id;
			let type = option.type;
			let price = option.price;
			if (!this.$wechat.isWeixin()) {
				if(type === 'order'){
					this.link = location.protocol + '//' + window.location.host + '/pages/users/alipay_invoke/index?orderNo='+this.orderId + '&type=order';
					if (!this.orderId) {
						this.content = '支付订单不存在，页面将在2秒后自动关闭！';
						uni.showToast({
							title: '支付订单不存在,页面将在2秒后自动关闭',
							icon: 'none'
						});
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 2000);
					}
					uni.showLoading({
						title: '正在支付中'
					});
					orderPay({
						orderNo: this.orderId,
						payChannel: 'alipay',
						payType: 'alipay'
					}).then(res=>{
						uni.hideLoading(); 
						that.$nextTick(() => {
							document.forms['punchout_form'].submit();
						})
					}).catch(err=>{
						uni.hideLoading();
							uni.showToast({
								title: err,
								icon: 'none'
							});
							setTimeout(() => {
								uni.switchTab({
									url: '/pages/index/index'
								});
							}, 2000);
					})
				}
			
			}
		},
		onReady() {
			this.$nextTick(() => {
				// #ifdef H5
				const clipboard = new ClipboardJS(".copy");
				clipboard.on("success", () => {
					uni.showToast({
						title: '复制成功'
					});
				});
				// #endif
			});
		},
		methods: {
			// #ifdef MP
			copyLink() {
				uni.setClipboardData({
					data: this.link,
					success() {
						uni.showToast({
							title: '复制成功',
							icon: 'success'
						});
					},
					fail() {
						uni.showToast({
							title: '复制失败',
							icon: 'none'
						});
					}
				});
			},
			// #endif
			goDetail() {
				if(this.orderId){
					uni.navigateTo({
						url: `/pages/order/order_details/index?order_id=${this.orderId}`
					});
				}else{
					uni.navigateTo({
						url: `/pages/users/user_money/index`
					});
				}
				
			}
		}
	};
	//#endif
</script>

<style>
	page {
		background-color: #ffffff;
	}
</style>

<style lang="scss" scoped>
	.alipaysubmit {
		display: none;
	}

	.text-section {
		padding-top: 148rpx;
		margin-top: 185rpx;
		background: url('data:image/png;base64,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') center top/280rpx 98rpx no-repeat;
		font-weight: bold;
		font-size: 32rpx;
		text-align: center;
		color: #111111;

		.link {
			margin-top: 16rpx;
			margin-right: 98rpx;
			margin-left: 98rpx;
			white-space: pre-wrap;
			overflow-wrap: break-word;
			font-weight: normal;
			color: #666666;
		}
	}

	.button-section {
		margin-top: 95rpx;
		margin-right: 98rpx;
		margin-left: 98rpx;

		.button {
			height: 80rpx;
			border-radius: 40rpx;
			font-size: 30rpx;
			line-height: 80rpx;

			~.button {
				margin-top: 32rpx;
			}

			&.copy {
				background-color: #333333;
				color: #ffffff;
			}

			&.off {
				border: 2rpx solid #999999;
				color: #666666;
			}
		}
	}

	.hint {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		padding-top: 156rpx;
		padding-left: 144rpx;
		background: url("data:image/png;base64,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") calc(100% - 32rpx) 12rpx/229rpx 178rpx no-repeat;
		font-weight: 500;
		font-size: 30rpx;
		line-height: 68rpx;
		color: #c1c1c1;
		background-color: rgba(0, 0, 0, 0.8);

		.iconfont {
			margin-left: 10rpx;
			font-weight: bold;
			font-size: 30rpx;
		}
	}
</style>
