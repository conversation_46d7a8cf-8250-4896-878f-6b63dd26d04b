{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?3673", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?f428", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?02ae", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?7289", "uni-app:///pages/order/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?c5ac", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/order/index.vue?9188"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "currentTab", "loading", "orderList", "statusTabs", "label", "value", "onLoad", "onReachBottom", "methods", "loadOrderList", "setTimeout", "id", "orderNo", "userName", "totalAmount", "status", "statusText", "createTime", "console", "uni", "title", "icon", "switchTab", "handleSearch", "loadMore", "viewOrderDetail", "url", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4D77B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;kBACAC;oBACA,mBACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAN;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAN;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,EACA;oBACA;kBACA;gBACA;kBACAC;kBACAC;oBACAC;oBACAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACAN;QACAO;MACA;IACA;IAEA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=0ca91b30&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0ca91b30\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=0ca91b30&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.orderList, function (order, index) {\n    var $orig = _vm.__get_orig(order)\n    var m0 = _vm.formatTime(order.createTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.loading && _vm.orderList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"order-page\">\n    <view class=\"search-bar\">\n      <input \n        v-model=\"searchKeyword\" \n        placeholder=\"搜索订单号、用户名\" \n        class=\"search-input\"\n        @confirm=\"handleSearch\"\n      />\n      <button class=\"search-btn\" @click=\"handleSearch\">搜索</button>\n    </view>\n    \n    <view class=\"filter-tabs\">\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: currentTab === item.value }\"\n        v-for=\"item in statusTabs\" \n        :key=\"item.value\"\n        @click=\"switchTab(item.value)\"\n      >\n        {{ item.label }}\n      </view>\n    </view>\n    \n    <view class=\"order-list\">\n      <view \n        class=\"order-item\" \n        v-for=\"(order, index) in orderList\" \n        :key=\"index\"\n        @click=\"viewOrderDetail(order)\"\n      >\n        <view class=\"order-header\">\n          <text class=\"order-no\">{{ order.orderNo }}</text>\n          <text class=\"order-status\" :class=\"'status-' + order.status\">\n            {{ order.statusText || '未知状态' }}\n          </text>\n        </view>\n        \n        <view class=\"order-info\">\n          <text class=\"user-name\">用户：{{ order.userName }}</text>\n          <text class=\"order-time\">{{ formatTime(order.createTime) }}</text>\n        </view>\n        \n        <view class=\"order-amount\">\n          <text class=\"amount\">¥{{ order.totalAmount }}</text>\n        </view>\n      </view>\n    </view>\n    \n    <view v-if=\"loading\" class=\"loading\">\n      <text>加载中...</text>\n    </view>\n    \n    <view v-if=\"!loading && orderList.length === 0\" class=\"empty\">\n      <text>暂无订单数据</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      searchKeyword: '',\n      currentTab: 'all',\n      loading: false,\n      orderList: [],\n      statusTabs: [\n        { label: '全部', value: 'all' },\n        { label: '待付款', value: 'unpaid' },\n        { label: '待发货', value: 'unshipped' },\n        { label: '已完成', value: 'completed' }\n      ]\n    }\n  },\n  \n  onLoad() {\n    this.loadOrderList()\n  },\n  \n  onReachBottom() {\n    this.loadMore()\n  },\n  \n  methods: {\n    async loadOrderList() {\n      this.loading = true\n      try {\n        // 模拟订单数据\n        setTimeout(() => {\n          this.orderList = [\n            {\n              id: 1,\n              orderNo: 'ORD20250128001',\n              userName: '张三',\n              totalAmount: '299.00',\n              status: 'completed',\n              statusText: '已完成',\n              createTime: new Date().toISOString()\n            },\n            {\n              id: 2,\n              orderNo: 'ORD20250128002',\n              userName: '李四',\n              totalAmount: '158.50',\n              status: 'unshipped',\n              statusText: '待发货',\n              createTime: new Date().toISOString()\n            },\n            {\n              id: 3,\n              orderNo: 'ORD20250128003',\n              userName: '王五',\n              totalAmount: '89.90',\n              status: 'unpaid',\n              statusText: '待付款',\n              createTime: new Date().toISOString()\n            }\n          ]\n          this.loading = false\n        }, 1000)\n      } catch (error) {\n        console.error('加载订单失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n        this.loading = false\n      }\n    },\n    \n    switchTab(tab) {\n      this.currentTab = tab\n      this.loadOrderList()\n    },\n    \n    handleSearch() {\n      this.loadOrderList()\n    },\n    \n    loadMore() {\n      // 实现分页加载\n    },\n    \n    viewOrderDetail(order) {\n      uni.navigateTo({\n        url: `/pages/order/detail?id=${order.id}`\n      })\n    },\n    \n    // 移除了 getStatusClass 和 getStatusText 方法，改用模板中的简化语法\n    \n    formatTime(time) {\n      return new Date(time).toLocaleString()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.order-page {\n  background: $crmeb-bg-color;\n  min-height: 100vh;\n}\n\n.search-bar {\n  display: flex;\n  padding: 20rpx;\n  background: white;\n  \n  .search-input {\n    flex: 1;\n    height: 70rpx;\n    border: 2rpx solid #e4e7ed;\n    border-radius: 8rpx;\n    padding: 0 20rpx;\n    margin-right: 20rpx;\n  }\n  \n  .search-btn {\n    width: 120rpx;\n    height: 70rpx;\n    background: $crmeb-color-primary;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n  }\n}\n\n.filter-tabs {\n  display: flex;\n  background: white;\n  border-top: 1rpx solid #f0f0f0;\n  \n  .tab-item {\n    flex: 1;\n    text-align: center;\n    padding: 30rpx 0;\n    font-size: 28rpx;\n    color: #666;\n    border-bottom: 4rpx solid transparent;\n    \n    &.active {\n      color: $crmeb-color-primary;\n      border-bottom-color: $crmeb-color-primary;\n    }\n  }\n}\n\n.order-list {\n  padding: 20rpx;\n}\n\n.order-item {\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .order-header {\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    margin-bottom: 20rpx;\n    \n    .order-no {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n    }\n    \n    .order-status {\n      padding: 10rpx 20rpx;\n      border-radius: 20rpx;\n      font-size: 24rpx;\n      \n      &.status-unpaid {\n        background: rgba(255, 153, 0, 0.1);\n        color: $crmeb-color-warning;\n      }\n\n      &.status-unshipped {\n        background: rgba(45, 183, 245, 0.1);\n        color: $crmeb-color-info;\n      }\n\n      &.status-completed {\n        background: rgba(25, 190, 107, 0.1);\n        color: $crmeb-color-success;\n      }\n    }\n  }\n  \n  .order-info {\n    display: flex;\n    justify-content: space-between;\n    margin-bottom: 20rpx;\n    \n    .user-name, .order-time {\n      font-size: 26rpx;\n      color: #999;\n    }\n  }\n  \n  .order-amount {\n    text-align: right;\n    \n    .amount {\n      font-size: 36rpx;\n      font-weight: bold;\n      color: $crmeb-color-primary;\n    }\n  }\n}\n\n.loading, .empty {\n  text-align: center;\n  padding: 80rpx;\n  color: #999;\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=0ca91b30&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685164118\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}