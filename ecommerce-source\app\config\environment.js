/**
 * 环境配置管理
 * 解决审计报告中的硬编码配置问题
 */

// 环境类型
const ENV_TYPES = {
  DEVELOPMENT: 'development',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// 当前环境检测
function getCurrentEnvironment() {
  // #ifdef MP-WEIXIN
  // 微信小程序环境检测
  try {
    const accountInfo = wx.getAccountInfoSync();
    if (accountInfo.miniProgram.envVersion === 'develop') {
      return ENV_TYPES.DEVELOPMENT;
    } else if (accountInfo.miniProgram.envVersion === 'trial') {
      return ENV_TYPES.TESTING;
    } else {
      return ENV_TYPES.PRODUCTION;
    }
  } catch (error) {
    return ENV_TYPES.DEVELOPMENT;
  }
  // #endif
  
  // #ifdef H5
  // H5环境检测
  if (location.hostname === 'localhost' || location.hostname === '127.0.0.1') {
    return ENV_TYPES.DEVELOPMENT;
  } else if (location.hostname.includes('test') || location.hostname.includes('staging')) {
    return ENV_TYPES.TESTING;
  } else {
    return ENV_TYPES.PRODUCTION;
  }
  // #endif
  
  // #ifdef APP-PLUS
  // APP环境检测（可以通过打包配置来区分）
  return process.env.NODE_ENV === 'production' ? ENV_TYPES.PRODUCTION : ENV_TYPES.DEVELOPMENT;
  // #endif
  
  return ENV_TYPES.DEVELOPMENT;
}

// 环境配置
const ENVIRONMENT_CONFIG = {
  [ENV_TYPES.DEVELOPMENT]: {
    // 开发环境配置
    api: {
      baseUrl: 'http://localhost:8080',
      timeout: 15000,
      enableMock: true,
      enableSignature: false,
      enableEncryption: false
    },
    monitor: {
      enabled: true,
      reportUrl: '',
      logLevel: 'debug',
      enableConsoleLog: true
    },
    security: {
      enableHttps: false,
      enableTokenRefresh: true,
      tokenExpiry: 2 * 60 * 60 * 1000, // 2小时
      maxRetryCount: 3
    },
    performance: {
      enableCache: true,
      cacheExpiry: 5 * 60 * 1000, // 5分钟
      enableLazyLoad: true,
      enableVirtualScroll: false
    },
    features: {
      enableDebugMode: true,
      enableVConsole: true,
      enablePerformanceMonitor: true
    }
  },
  
  [ENV_TYPES.TESTING]: {
    // 测试环境配置
    api: {
      baseUrl: 'https://test-api.crmeb.net',
      timeout: 10000,
      enableMock: false,
      enableSignature: true,
      enableEncryption: false
    },
    monitor: {
      enabled: true,
      reportUrl: 'https://test-monitor.crmeb.net/report',
      logLevel: 'info',
      enableConsoleLog: true
    },
    security: {
      enableHttps: true,
      enableTokenRefresh: true,
      tokenExpiry: 2 * 60 * 60 * 1000, // 2小时
      maxRetryCount: 3
    },
    performance: {
      enableCache: true,
      cacheExpiry: 10 * 60 * 1000, // 10分钟
      enableLazyLoad: true,
      enableVirtualScroll: true
    },
    features: {
      enableDebugMode: true,
      enableVConsole: false,
      enablePerformanceMonitor: true
    }
  },
  
  [ENV_TYPES.STAGING]: {
    // 预发布环境配置
    api: {
      baseUrl: 'https://staging-api.crmeb.net',
      timeout: 8000,
      enableMock: false,
      enableSignature: true,
      enableEncryption: true
    },
    monitor: {
      enabled: true,
      reportUrl: 'https://staging-monitor.crmeb.net/report',
      logLevel: 'warn',
      enableConsoleLog: false
    },
    security: {
      enableHttps: true,
      enableTokenRefresh: true,
      tokenExpiry: 4 * 60 * 60 * 1000, // 4小时
      maxRetryCount: 2
    },
    performance: {
      enableCache: true,
      cacheExpiry: 30 * 60 * 1000, // 30分钟
      enableLazyLoad: true,
      enableVirtualScroll: true
    },
    features: {
      enableDebugMode: false,
      enableVConsole: false,
      enablePerformanceMonitor: true
    }
  },
  
  [ENV_TYPES.PRODUCTION]: {
    // 生产环境配置
    api: {
      baseUrl: 'https://api.crmeb.net',
      timeout: 5000,
      enableMock: false,
      enableSignature: true,
      enableEncryption: true
    },
    monitor: {
      enabled: true,
      reportUrl: 'https://monitor.crmeb.net/report',
      logLevel: 'error',
      enableConsoleLog: false
    },
    security: {
      enableHttps: true,
      enableTokenRefresh: true,
      tokenExpiry: 8 * 60 * 60 * 1000, // 8小时
      maxRetryCount: 1
    },
    performance: {
      enableCache: true,
      cacheExpiry: 60 * 60 * 1000, // 1小时
      enableLazyLoad: true,
      enableVirtualScroll: true
    },
    features: {
      enableDebugMode: false,
      enableVConsole: false,
      enablePerformanceMonitor: false
    }
  }
};

// 获取当前环境配置
function getEnvironmentConfig() {
  const currentEnv = getCurrentEnvironment();
  const config = ENVIRONMENT_CONFIG[currentEnv];
  
  if (!config) {
    console.warn(`未找到环境配置: ${currentEnv}，使用开发环境配置`);
    return ENVIRONMENT_CONFIG[ENV_TYPES.DEVELOPMENT];
  }
  
  return {
    ...config,
    environment: currentEnv,
    isProduction: currentEnv === ENV_TYPES.PRODUCTION,
    isDevelopment: currentEnv === ENV_TYPES.DEVELOPMENT,
    isTesting: currentEnv === ENV_TYPES.TESTING,
    isStaging: currentEnv === ENV_TYPES.STAGING
  };
}

// 配置验证
function validateConfig(config) {
  const requiredFields = [
    'api.baseUrl',
    'monitor.enabled',
    'security.enableHttps',
    'performance.enableCache'
  ];
  
  const errors = [];
  
  requiredFields.forEach(field => {
    const keys = field.split('.');
    let value = config;
    
    for (const key of keys) {
      if (value && typeof value === 'object' && key in value) {
        value = value[key];
      } else {
        errors.push(`缺少必需的配置项: ${field}`);
        break;
      }
    }
  });
  
  if (errors.length > 0) {
    console.error('配置验证失败:', errors);
    return false;
  }
  
  return true;
}

// 动态配置更新
function updateConfig(updates) {
  const currentConfig = getEnvironmentConfig();
  const newConfig = { ...currentConfig, ...updates };
  
  if (validateConfig(newConfig)) {
    // 这里可以实现配置的动态更新逻辑
    console.log('配置更新成功');
    return newConfig;
  } else {
    console.error('配置更新失败：验证不通过');
    return currentConfig;
  }
}

// 获取特定配置项
function getConfig(path, defaultValue = null) {
  const config = getEnvironmentConfig();
  const keys = path.split('.');
  let value = config;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return defaultValue;
    }
  }
  
  return value;
}

// 导出配置管理器
const EnvironmentManager = {
  getCurrentEnvironment,
  getEnvironmentConfig,
  validateConfig,
  updateConfig,
  getConfig,
  ENV_TYPES,
  
  // 便捷方法
  isProduction: () => getCurrentEnvironment() === ENV_TYPES.PRODUCTION,
  isDevelopment: () => getCurrentEnvironment() === ENV_TYPES.DEVELOPMENT,
  isTesting: () => getCurrentEnvironment() === ENV_TYPES.TESTING,
  isStaging: () => getCurrentEnvironment() === ENV_TYPES.STAGING,
  
  // 获取API配置
  getApiConfig: () => getConfig('api'),
  
  // 获取监控配置
  getMonitorConfig: () => getConfig('monitor'),
  
  // 获取安全配置
  getSecurityConfig: () => getConfig('security'),
  
  // 获取性能配置
  getPerformanceConfig: () => getConfig('performance'),
  
  // 获取功能开关配置
  getFeaturesConfig: () => getConfig('features')
};

export default EnvironmentManager;
