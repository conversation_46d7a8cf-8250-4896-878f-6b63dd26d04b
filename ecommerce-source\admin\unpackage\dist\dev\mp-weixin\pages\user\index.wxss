@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.user-page.data-v-137d5072 {
  background: #f8f8f9;
  min-height: 100vh;
}
.search-bar.data-v-137d5072 {
  display: flex;
  padding: 20rpx;
  background: white;
}
.search-bar .search-input.data-v-137d5072 {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-bar .search-btn.data-v-137d5072 {
  width: 120rpx;
  height: 70rpx;
  background: #E93323;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.user-stats.data-v-137d5072 {
  display: flex;
  background: white;
  margin: 20rpx;
  border-radius: 16rpx;
  padding: 40rpx;
}
.user-stats .stat-item.data-v-137d5072 {
  flex: 1;
  text-align: center;
}
.user-stats .stat-item .stat-value.data-v-137d5072 {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #E93323;
  margin-bottom: 10rpx;
}
.user-stats .stat-item .stat-label.data-v-137d5072 {
  font-size: 24rpx;
  color: #999;
}
.user-list.data-v-137d5072 {
  padding: 0 20rpx;
}
.user-item.data-v-137d5072 {
  display: flex;
  align-items: center;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.user-item .user-avatar.data-v-137d5072 {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30rpx;
}
.user-item .user-avatar image.data-v-137d5072 {
  width: 100%;
  height: 100%;
}
.user-item .user-info.data-v-137d5072 {
  flex: 1;
}
.user-item .user-info .user-name.data-v-137d5072 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}
.user-item .user-info .user-phone.data-v-137d5072 {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.user-item .user-info .user-time.data-v-137d5072 {
  font-size: 24rpx;
  color: #999;
}
.user-item .user-data.data-v-137d5072 {
  text-align: right;
}
.user-item .user-data .user-orders.data-v-137d5072, .user-item .user-data .user-amount.data-v-137d5072 {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
}
.user-item .user-data .user-amount.data-v-137d5072 {
  color: #E93323;
  font-weight: bold;
}
.loading.data-v-137d5072, .empty.data-v-137d5072 {
  text-align: center;
  padding: 80rpx;
  color: #999;
}
