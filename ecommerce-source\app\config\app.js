// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2025 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
// 引入环境配置管理
import EnvironmentManager from './environment.js';

//移动端商城API - 使用环境配置管理
const envConfig = EnvironmentManager.getEnvironmentConfig();
const apiConfig = envConfig.api;

let domain = apiConfig.baseUrl;

// 如果启用了模拟模式且API不可用，使用演示域名
if (apiConfig.enableMock && !domain.includes('localhost')) {
  domain = 'https://demo.crmeb.net';
}

console.log('=== CRMEB API配置 ===', {
  environment: envConfig.environment,
  domain: domain,
  timeout: apiConfig.timeout,
  enableMock: apiConfig.enableMock,
  enableSignature: apiConfig.enableSignature
});

// 图片域名配置 - 确保使用HTTPS协议
const IMAGE_DOMAIN = 'https://cdn.crmeb.net'
console.log('=== 图片域名配置 ===', IMAGE_DOMAIN);

module.exports = {
	// 请求域名 格式： https://您的域名
	// #ifdef MP || APP-PLUS
		// HTTP_REQUEST_URL:'',
		HTTP_REQUEST_URL: domain,
		// H5商城地址
		HTTP_H5_URL: 'https://java.crmeb.net',
	// #endif
	// #ifdef H5
		HTTP_REQUEST_URL:domain,
	// #endif
	// 图片域名配置 - 解决图片加载失败问题
	IMAGE_DOMAIN: IMAGE_DOMAIN,
	HEADER:{
		'content-type': 'application/json'
	},
	HEADERPARAMS:{
		'content-type': 'application/x-www-form-urlencoded'
	},
	// 回话密钥名称 请勿修改此配置
	TOKENNAME: 'Authori-zation',
	// 缓存时间 0 永久
	EXPIRE:0,
	//分页最多显示条数
	LIMIT: 10
};
