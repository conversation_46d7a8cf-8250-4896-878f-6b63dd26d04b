@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.login-container.data-v-4586967a {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
}
.login-form.data-v-4586967a {
  background: white;
  border-radius: 20rpx;
  padding: 80rpx 60rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.1);
}
.logo.data-v-4586967a {
  text-align: center;
  margin-bottom: 80rpx;
}
.logo image.data-v-4586967a {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}
.logo .title.data-v-4586967a {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.form-item.data-v-4586967a {
  margin-bottom: 40rpx;
}
.input.data-v-4586967a {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
}
.input.data-v-4586967a:focus {
  border-color: #E93323;
}
.login-btn.data-v-4586967a {
  width: 100%;
  height: 88rpx;
  background: #E93323;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 32rpx;
  font-weight: bold;
  margin-top: 40rpx;
}
.login-btn.data-v-4586967a:hover {
  background: #c52214;
}
