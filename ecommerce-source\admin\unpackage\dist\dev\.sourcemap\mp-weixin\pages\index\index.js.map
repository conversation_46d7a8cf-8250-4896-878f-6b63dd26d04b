{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/index/index.vue?8639", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/index/index.vue?bc66", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/index/index.vue?6d7a", "uni-app:///pages/index/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/index/index.vue?c350", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/index/index.vue?904d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "apiUrl", "version", "systemInfo", "onLoad", "methods", "getSystemInfo", "uni", "success", "goToLogin", "url", "goToDashboard", "goToOrder", "goToUser", "goToStore", "testApi", "title", "method", "account", "pwd", "icon", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC2D77B;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MAAA;MACAC;QACAC;UACA;QACA;MACA;IACA;IAEAC;MACAF;QACAG;MACA;IACA;IAEAC;MACAJ;QACAG;MACA;IACA;IAEAE;MACAL;QACAG;MACA;IACA;IAEAG;MACAN;QACAG;MACA;IACA;IAEAI;MACAP;QACAG;MACA;IACA;IAEAK;MACAR;QACAS;MACA;;MAEA;MACAT;QACAG;QACAO;QACAjB;UACAkB;UACAC;QACA;QACAX;UACAD;UACAA;YACAS;YACAI;UACA;QACA;QACAC;UACAd;UACAA;YACAS;YACAI;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7IA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">🎉 CRMEB 小程序管理后台</text>\n      <text class=\"subtitle\">项目配置成功！</text>\n    </view>\n    \n    <view class=\"content\">\n      <view class=\"card\">\n        <text class=\"card-title\">✅ 编译状态</text>\n        <text class=\"card-desc\">项目已成功编译，可以正常运行</text>\n      </view>\n      \n      <view class=\"card\">\n        <text class=\"card-title\">🔧 配置信息</text>\n        <text class=\"card-desc\">API地址：{{ apiUrl }}</text>\n        <text class=\"card-desc\">版本：{{ version }}</text>\n      </view>\n      \n      <view class=\"card\">\n        <text class=\"card-title\">📱 系统信息</text>\n        <text class=\"card-desc\">平台：{{ systemInfo.platform }}</text>\n        <text class=\"card-desc\">版本：{{ systemInfo.version }}</text>\n      </view>\n    </view>\n    \n    <view class=\"navigation\">\n      <text class=\"nav-title\">🚀 管理功能</text>\n      <view class=\"nav-grid\">\n        <view class=\"nav-item\" @click=\"goToLogin\">\n          <text class=\"nav-icon\">🔐</text>\n          <text class=\"nav-text\">登录</text>\n        </view>\n        <view class=\"nav-item\" @click=\"goToDashboard\">\n          <text class=\"nav-icon\">📊</text>\n          <text class=\"nav-text\">控制台</text>\n        </view>\n        <view class=\"nav-item\" @click=\"goToOrder\">\n          <text class=\"nav-icon\">📦</text>\n          <text class=\"nav-text\">订单管理</text>\n        </view>\n        <view class=\"nav-item\" @click=\"goToUser\">\n          <text class=\"nav-icon\">👥</text>\n          <text class=\"nav-text\">用户管理</text>\n        </view>\n        <view class=\"nav-item\" @click=\"goToStore\">\n          <text class=\"nav-icon\">🛍️</text>\n          <text class=\"nav-text\">商品管理</text>\n        </view>\n        <view class=\"nav-item\" @click=\"testApi\">\n          <text class=\"nav-icon\">🔧</text>\n          <text class=\"nav-text\">测试API</text>\n        </view>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      apiUrl: 'http://localhost:20500/api',\n      version: '4.2.1',\n      systemInfo: {}\n    }\n  },\n  \n  onLoad() {\n    this.getSystemInfo()\n  },\n  \n  methods: {\n    getSystemInfo() {\n      uni.getSystemInfo({\n        success: (res) => {\n          this.systemInfo = res\n        }\n      })\n    },\n    \n    goToLogin() {\n      uni.navigateTo({\n        url: '/pages/login/index'\n      })\n    },\n\n    goToDashboard() {\n      uni.navigateTo({\n        url: '/pages/dashboard/index'\n      })\n    },\n\n    goToOrder() {\n      uni.navigateTo({\n        url: '/pages/order/index'\n      })\n    },\n\n    goToUser() {\n      uni.navigateTo({\n        url: '/pages/user/index'\n      })\n    },\n\n    goToStore() {\n      uni.navigateTo({\n        url: '/pages/store/index'\n      })\n    },\n    \n    testApi() {\n      uni.showLoading({\n        title: '测试中...'\n      })\n      \n      // 简单的网络测试\n      uni.request({\n        url: this.apiUrl + '/admin/login',\n        method: 'POST',\n        data: {\n          account: 'test',\n          pwd: 'test'\n        },\n        success: (res) => {\n          uni.hideLoading()\n          uni.showToast({\n            title: 'API连接正常',\n            icon: 'success'\n          })\n        },\n        fail: (err) => {\n          uni.hideLoading()\n          uni.showToast({\n            title: 'API连接失败',\n            icon: 'none'\n          })\n        }\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.container {\n  padding: 40rpx;\n  min-height: 100vh;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n}\n\n.header {\n  text-align: center;\n  margin-bottom: 60rpx;\n  \n  .title {\n    display: block;\n    font-size: 48rpx;\n    font-weight: bold;\n    color: white;\n    margin-bottom: 20rpx;\n  }\n  \n  .subtitle {\n    font-size: 28rpx;\n    color: rgba(255, 255, 255, 0.8);\n  }\n}\n\n.content {\n  margin-bottom: 60rpx;\n}\n\n.card {\n  background: white;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  margin-bottom: 30rpx;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\n  \n  .card-title {\n    display: block;\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n  \n  .card-desc {\n    display: block;\n    font-size: 26rpx;\n    color: #666;\n    margin-bottom: 10rpx;\n    \n    &:last-child {\n      margin-bottom: 0;\n    }\n  }\n}\n\n.navigation {\n  margin-bottom: 40rpx;\n}\n\n.nav-title {\n  display: block;\n  font-size: 36rpx;\n  font-weight: bold;\n  color: white;\n  text-align: center;\n  margin-bottom: 40rpx;\n}\n\n.nav-grid {\n  display: grid;\n  grid-template-columns: repeat(3, 1fr);\n  gap: 20rpx;\n}\n\n.nav-item {\n  background: white;\n  border-radius: 16rpx;\n  padding: 40rpx 20rpx;\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);\n  transition: transform 0.2s;\n\n  &:active {\n    transform: scale(0.95);\n  }\n}\n\n.nav-icon {\n  font-size: 48rpx;\n  margin-bottom: 16rpx;\n}\n\n.nav-text {\n  font-size: 24rpx;\n  color: #333;\n  font-weight: 500;\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685542506\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}