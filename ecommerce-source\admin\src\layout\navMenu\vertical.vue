<template>
  <div>
    <el-menu
      router
      :class="setColumnsAsideStyle"
      background-color="transparent"
      :default-active="activePath || defaultActive"
      :collapse="setIsCollapse"
      :unique-opened="getThemeConfig.isUniqueOpened"
      :collapse-transition="true"
    >
      <template v-for="val in menuList">
        <el-submenu :index="val.path" v-if="val.children && val.children.length > 0" :key="val.path">
          <template slot="title">
            <i class="ivu-icon" :class="val.icon ? 'el-icon-' + val.icon : ''"></i>
            <span>{{ val.title }}</span>
          </template>
          <SubItem :chil="val.children" />
        </el-submenu>
        <template v-else>
          <el-menu-item :index="val.path" :key="val.path">
            <i class="ivu-icon" :class="val.icon ? 'el-icon-' + val.icon : ''"></i>
            <template slot="title">
              <span>{{ val.title }}</span>
            </template>
          </el-menu-item>
        </template>
      </template>
    </el-menu>
  </div>
</template>

<script>
import SubItem from '@/layout/navMenu/subItem.vue';
import { mapState } from 'vuex';

export default {
  name: 'navMenuVertical',
  components: { SubItem },
  props: {
    menuList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  data() {
    return {
      defaultActive: this.$route.path,
      onRoutes: '',
    };
  },
  computed: {
    ...mapState('menu', ['activePath']),
    // 设置分栏高亮风格
    setColumnsAsideStyle() {
      return this.$store.state.themeConfig.themeConfig.columnsAsideStyle;
    },
    // 获取布局配置信息
    getThemeConfig() {
      return this.$store.state.themeConfig.themeConfig;
    },
    // 设置左侧菜单是否展开/收起
    setIsCollapse() {
      return document.body.clientWidth < 1000 ? false : this.$store.state.themeConfig.themeConfig.isCollapse;
    },
  },
  watch: {
    // 监听路由的变化
    $route: {
      handler(to) {
        this.defaultActive = to.path;
        const clientWidth = document.body.clientWidth;
        if (clientWidth < 1000) this.$store.state.themeConfig.themeConfig.isCollapse = false;
      },
      deep: true,
    },
  },
  created() {},
};
</script>
<style lang="scss" scoped>
:deep(.center) {
  text-align: center;
  margin-right: 0 !important;
  margin-left: 5px;
}

// 科技感菜单样式
:deep(.el-menu) {
  background: transparent !important;
  border: none;

  .el-menu-item {
    background: transparent !important;
    color: var(--text-secondary) !important;
    border-radius: 8px !important;
    margin: 4px 8px !important;
    transition: all 0.3s ease !important;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 0;
      width: 3px;
      height: 100%;
      background: transparent;
      transition: all 0.3s ease;
    }

    &:hover {
      background: rgba(0, 212, 255, 0.1) !important;
      color: var(--primary-color) !important;
      transform: translateX(4px);
      box-shadow: 0 4px 12px rgba(0, 212, 255, 0.2);

      &::before {
        background: var(--gradient-primary);
      }

      .ivu-icon {
        color: var(--primary-color) !important;
        text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
      }
    }

    &.is-active {
      background: var(--gradient-primary) !important;
      color: var(--text-primary) !important;
      box-shadow: var(--shadow-glow);

      &::before {
        background: #ffffff;
      }

      .ivu-icon {
        color: var(--text-primary) !important;
      }
    }

    .ivu-icon {
      color: var(--text-muted);
      transition: all 0.3s ease;
      margin-right: 8px;
    }
  }

  .el-submenu {
    .el-submenu__title {
      background: transparent !important;
      color: var(--text-secondary) !important;
      border-radius: 8px !important;
      margin: 4px 8px !important;
      transition: all 0.3s ease !important;
      position: relative;

      &:hover {
        background: rgba(0, 212, 255, 0.1) !important;
        color: var(--primary-color) !important;
        transform: translateX(4px);

        .ivu-icon {
          color: var(--primary-color) !important;
          text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
        }

        .el-submenu__icon-arrow {
          color: var(--primary-color) !important;
        }
      }

      .ivu-icon {
        color: var(--text-muted);
        transition: all 0.3s ease;
        margin-right: 8px;
      }

      .el-submenu__icon-arrow {
        color: var(--text-muted);
        transition: all 0.3s ease;
      }
    }

    &.is-opened {
      .el-submenu__title {
        color: var(--primary-color) !important;

        .ivu-icon {
          color: var(--primary-color) !important;
        }

        .el-submenu__icon-arrow {
          color: var(--primary-color) !important;
        }
      }
    }

    .el-menu {
      background: rgba(0, 212, 255, 0.02) !important;
      border-radius: 8px;
      margin: 4px 16px;
      border: 1px solid rgba(0, 212, 255, 0.1);
    }
  }

  // 折叠状态样式
  &.el-menu--collapse {
    .el-menu-item {
      margin: 4px 4px !important;

      &:hover {
        transform: scale(1.05);
      }
    }

    .el-submenu {
      .el-submenu__title {
        margin: 4px 4px !important;

        &:hover {
          transform: scale(1.05);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  :deep(.el-menu) {
    .el-menu-item {
      margin: 2px 4px !important;
      padding: 0 16px !important;
    }

    .el-submenu {
      .el-submenu__title {
        margin: 2px 4px !important;
        padding: 0 16px !important;
      }
    }
  }
}
</style>
