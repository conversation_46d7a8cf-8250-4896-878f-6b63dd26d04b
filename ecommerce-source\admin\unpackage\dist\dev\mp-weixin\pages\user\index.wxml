<view class="user-page data-v-137d5072"><view class="search-bar data-v-137d5072"><input class="search-input data-v-137d5072" placeholder="搜索用户昵称、手机号" data-event-opts="{{[['confirm',[['handleSearch',['$event']]]],['input',[['__set_model',['','searchKeyword','$event',[]]]]]]}}" value="{{searchKeyword}}" bindconfirm="__e" bindinput="__e"/><button data-event-opts="{{[['tap',[['handleSearch',['$event']]]]]}}" class="search-btn data-v-137d5072" bindtap="__e">搜索</button></view><view class="user-stats data-v-137d5072"><view class="stat-item data-v-137d5072"><text class="stat-value data-v-137d5072">{{totalUsers}}</text><text class="stat-label data-v-137d5072">总用户数</text></view><view class="stat-item data-v-137d5072"><text class="stat-value data-v-137d5072">{{todayNewUsers}}</text><text class="stat-label data-v-137d5072">今日新增</text></view><view class="stat-item data-v-137d5072"><text class="stat-value data-v-137d5072">{{activeUsers}}</text><text class="stat-label data-v-137d5072">活跃用户</text></view></view><view class="user-list data-v-137d5072"><block wx:for="{{$root.l0}}" wx:for-item="user" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['viewUserDetail',['$0'],[[['userList','',index]]]]]]]}}" class="user-item data-v-137d5072" bindtap="__e"><view class="user-avatar data-v-137d5072"><image src="{{user.$orig.avatar||'/static/default-avatar.png'}}" mode="aspectFill" class="data-v-137d5072"></image></view><view class="user-info data-v-137d5072"><view class="user-name data-v-137d5072">{{user.$orig.nickname||'未设置昵称'}}</view><view class="user-phone data-v-137d5072">{{user.$orig.phone||'未绑定手机'}}</view><view class="user-time data-v-137d5072">{{"注册时间："+user.m0}}</view></view><view class="user-data data-v-137d5072"><view class="user-orders data-v-137d5072">{{"订单："+(user.$orig.orderCount||0)}}</view><view class="user-amount data-v-137d5072">{{"消费：¥"+(user.$orig.totalAmount||0)}}</view></view></view></block></view><block wx:if="{{loading}}"><view class="loading data-v-137d5072"><text class="data-v-137d5072">加载中...</text></view></block><block wx:if="{{$root.g0}}"><view class="empty data-v-137d5072"><text class="data-v-137d5072">暂无用户数据</text></view></block></view>