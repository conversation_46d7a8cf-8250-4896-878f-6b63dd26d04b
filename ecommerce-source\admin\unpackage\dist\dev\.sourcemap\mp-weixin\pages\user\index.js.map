{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?4b7e", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?a113", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?9add", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?ad96", "uni-app:///pages/user/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?0a94", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/user/index.vue?ae7c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "loading", "userList", "totalUsers", "todayNewUsers", "activeUsers", "onLoad", "onReachBottom", "methods", "loadUserStats", "setTimeout", "console", "loadUserList", "id", "nickname", "phone", "avatar", "orderCount", "totalAmount", "createTime", "uni", "title", "icon", "handleSearch", "loadMore", "viewUserDetail", "url", "formatTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1BA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8D77B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;oBACA;oBACA;oBACA;kBACA;gBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;kBACAF;oBACA,mBACA;sBACAG;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAN;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAN;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,EACA;oBACA;kBACA;gBACA;kBACAR;kBACAS;oBACAC;oBACAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACAL;QACAM;MACA;IACA;IAEAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjKA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/user/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/user/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=137d5072&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"137d5072\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/user/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=137d5072&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.userList, function (user, index) {\n    var $orig = _vm.__get_orig(user)\n    var m0 = _vm.formatTime(user.createTime)\n    return {\n      $orig: $orig,\n      m0: m0,\n    }\n  })\n  var g0 = !_vm.loading && _vm.userList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"user-page\">\n    <view class=\"search-bar\">\n      <input \n        v-model=\"searchKeyword\" \n        placeholder=\"搜索用户昵称、手机号\" \n        class=\"search-input\"\n        @confirm=\"handleSearch\"\n      />\n      <button class=\"search-btn\" @click=\"handleSearch\">搜索</button>\n    </view>\n    \n    <view class=\"user-stats\">\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ totalUsers }}</text>\n        <text class=\"stat-label\">总用户数</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ todayNewUsers }}</text>\n        <text class=\"stat-label\">今日新增</text>\n      </view>\n      <view class=\"stat-item\">\n        <text class=\"stat-value\">{{ activeUsers }}</text>\n        <text class=\"stat-label\">活跃用户</text>\n      </view>\n    </view>\n    \n    <view class=\"user-list\">\n      <view \n        class=\"user-item\" \n        v-for=\"(user, index) in userList\" \n        :key=\"index\"\n        @click=\"viewUserDetail(user)\"\n      >\n        <view class=\"user-avatar\">\n          <image :src=\"user.avatar || '/static/default-avatar.png'\" mode=\"aspectFill\"></image>\n        </view>\n        \n        <view class=\"user-info\">\n          <view class=\"user-name\">{{ user.nickname || '未设置昵称' }}</view>\n          <view class=\"user-phone\">{{ user.phone || '未绑定手机' }}</view>\n          <view class=\"user-time\">注册时间：{{ formatTime(user.createTime) }}</view>\n        </view>\n        \n        <view class=\"user-data\">\n          <view class=\"user-orders\">订单：{{ user.orderCount || 0 }}</view>\n          <view class=\"user-amount\">消费：¥{{ user.totalAmount || 0 }}</view>\n        </view>\n      </view>\n    </view>\n    \n    <view v-if=\"loading\" class=\"loading\">\n      <text>加载中...</text>\n    </view>\n    \n    <view v-if=\"!loading && userList.length === 0\" class=\"empty\">\n      <text>暂无用户数据</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      searchKeyword: '',\n      loading: false,\n      userList: [],\n      totalUsers: 0,\n      todayNewUsers: 0,\n      activeUsers: 0\n    }\n  },\n  \n  onLoad() {\n    this.loadUserStats()\n    this.loadUserList()\n  },\n  \n  onReachBottom() {\n    this.loadMore()\n  },\n  \n  methods: {\n    async loadUserStats() {\n      try {\n        // 模拟用户统计数据\n        setTimeout(() => {\n          this.totalUsers = 1234\n          this.todayNewUsers = 15\n          this.activeUsers = 567\n        }, 500)\n      } catch (error) {\n        console.error('加载用户统计失败:', error)\n      }\n    },\n\n    async loadUserList() {\n      this.loading = true\n      try {\n        // 模拟用户列表数据\n        setTimeout(() => {\n          this.userList = [\n            {\n              id: 1,\n              nickname: '张三',\n              phone: '138****1234',\n              avatar: '/static/default-avatar.png',\n              orderCount: 5,\n              totalAmount: '1299.00',\n              createTime: new Date().toISOString()\n            },\n            {\n              id: 2,\n              nickname: '李四',\n              phone: '139****5678',\n              avatar: '/static/default-avatar.png',\n              orderCount: 3,\n              totalAmount: '899.50',\n              createTime: new Date().toISOString()\n            },\n            {\n              id: 3,\n              nickname: '王五',\n              phone: '137****9012',\n              avatar: '/static/default-avatar.png',\n              orderCount: 8,\n              totalAmount: '2156.80',\n              createTime: new Date().toISOString()\n            }\n          ]\n          this.loading = false\n        }, 1000)\n      } catch (error) {\n        console.error('加载用户列表失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n        this.loading = false\n      }\n    },\n    \n    handleSearch() {\n      this.loadUserList()\n    },\n    \n    loadMore() {\n      // 实现分页加载\n    },\n    \n    viewUserDetail(user) {\n      uni.navigateTo({\n        url: `/pages/user/detail?id=${user.id}`\n      })\n    },\n    \n    formatTime(time) {\n      return new Date(time).toLocaleDateString()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.user-page {\n  background: $crmeb-bg-color;\n  min-height: 100vh;\n}\n\n.search-bar {\n  display: flex;\n  padding: 20rpx;\n  background: white;\n  \n  .search-input {\n    flex: 1;\n    height: 70rpx;\n    border: 2rpx solid #e4e7ed;\n    border-radius: 8rpx;\n    padding: 0 20rpx;\n    margin-right: 20rpx;\n  }\n  \n  .search-btn {\n    width: 120rpx;\n    height: 70rpx;\n    background: $crmeb-color-primary;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n  }\n}\n\n.user-stats {\n  display: flex;\n  background: white;\n  margin: 20rpx;\n  border-radius: 16rpx;\n  padding: 40rpx;\n  \n  .stat-item {\n    flex: 1;\n    text-align: center;\n    \n    .stat-value {\n      display: block;\n      font-size: 48rpx;\n      font-weight: bold;\n      color: $crmeb-color-primary;\n      margin-bottom: 10rpx;\n    }\n    \n    .stat-label {\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n}\n\n.user-list {\n  padding: 0 20rpx;\n}\n\n.user-item {\n  display: flex;\n  align-items: center;\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .user-avatar {\n    width: 100rpx;\n    height: 100rpx;\n    border-radius: 50%;\n    overflow: hidden;\n    margin-right: 30rpx;\n    \n    image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .user-info {\n    flex: 1;\n    \n    .user-name {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 10rpx;\n    }\n    \n    .user-phone {\n      font-size: 26rpx;\n      color: #666;\n      margin-bottom: 10rpx;\n    }\n    \n    .user-time {\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n  \n  .user-data {\n    text-align: right;\n    \n    .user-orders, .user-amount {\n      font-size: 24rpx;\n      color: #666;\n      margin-bottom: 10rpx;\n    }\n    \n    .user-amount {\n      color: $crmeb-color-primary;\n      font-weight: bold;\n    }\n  }\n}\n\n.loading, .empty {\n  text-align: center;\n  padding: 80rpx;\n  color: #999;\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=137d5072&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685164110\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}