<template>
  <view class="app">
    <!-- 小程序页面内容由页面路由自动渲染 -->
  </view>
</template>

<script>
export default {
  name: 'App',
  onLaunch() {
    console.log('CRMEB 小程序管理后台启动')
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 获取系统信息
    this.getSystemInfo()
  },
  
  onShow() {
    console.log('App Show')
  },
  
  onHide() {
    console.log('App Hide')
  },
  
  methods: {
    checkLoginStatus() {
      const token = uni.getStorageSync('token')
      if (!token) {
        // 如果没有token，跳转到登录页
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }
    },
    
    getSystemInfo() {
      // 使用新的 API 替代废弃的 wx.getSystemInfoSync
      Promise.all([
        new Promise(resolve => {
          if (typeof wx.getWindowInfo === 'function') {
            resolve(wx.getWindowInfo())
          } else {
            uni.getSystemInfo({ success: resolve })
          }
        }),
        new Promise(resolve => {
          if (typeof wx.getDeviceInfo === 'function') {
            resolve(wx.getDeviceInfo())
          } else {
            uni.getSystemInfo({ success: resolve })
          }
        }),
        new Promise(resolve => {
          if (typeof wx.getAppBaseInfo === 'function') {
            resolve(wx.getAppBaseInfo())
          } else {
            uni.getSystemInfo({ success: resolve })
          }
        })
      ]).then(([windowInfo, deviceInfo, appBaseInfo]) => {
        const systemInfo = Object.assign({}, windowInfo, deviceInfo, appBaseInfo)
        console.log('系统信息:', systemInfo)
        // 保存系统信息
        uni.setStorageSync('systemInfo', systemInfo)
      }).catch(err => {
        console.error('获取系统信息失败:', err)
        // 降级到旧 API
        uni.getSystemInfo({
          success: (res) => {
            console.log('系统信息:', res)
            uni.setStorageSync('systemInfo', res)
          }
        })
      })
    }
  }
}
</script>

<style lang="scss">
/* 全局样式 */
@import "uni.scss";

.app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 通用样式 */
page {
  background-color: $crmeb-bg-color;
  font-size: 28rpx;
  color: $crmeb-text-color;
}

/* 按钮样式重置 */
button {
  border: none;
  outline: none;
  background: none;
  padding: 0;
  margin: 0;
  border-radius: 0;
  
  &::after {
    border: none;
  }
}

/* 输入框样式重置 */
input {
  outline: none;
  border: none;
  background: none;
}

/* 通用工具类 */
.flex {
  display: flex;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-left {
  text-align: left;
}

.text-primary {
  color: $crmeb-color-primary;
}

.text-success {
  color: $crmeb-color-success;
}

.text-warning {
  color: $crmeb-color-warning;
}

.text-error {
  color: $crmeb-color-error;
}

.bg-primary {
  background-color: $crmeb-color-primary;
}

.bg-success {
  background-color: $crmeb-color-success;
}

.bg-warning {
  background-color: $crmeb-color-warning;
}

.bg-error {
  background-color: $crmeb-color-error;
}

/* 边距工具类 */
.m-0 { margin: 0; }
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.m-30 { margin: 30rpx; }

.p-0 { padding: 0; }
.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }

/* 圆角工具类 */
.radius-8 { border-radius: 8rpx; }
.radius-16 { border-radius: 16rpx; }
.radius-20 { border-radius: 20rpx; }
.radius-circle { border-radius: 50%; }

/* 阴影工具类 */
.shadow {
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.shadow-lg {
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
}
</style>
