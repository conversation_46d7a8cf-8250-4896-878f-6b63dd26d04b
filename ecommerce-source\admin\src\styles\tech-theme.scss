// 温和科技感主题样式
:root {
  // 主色调 - 柔和科技蓝
  --primary-color: #4a9eff;
  --primary-light: #6bb3ff;
  --primary-dark: #2d7dd2;

  // 背景色 - 更温和的深色
  --bg-primary: #1a1d23;
  --bg-secondary: #242933;
  --bg-tertiary: #2d3748;
  --bg-card: rgba(36, 41, 51, 0.85);
  --bg-glass: rgba(255, 255, 255, 0.03);

  // 文字颜色 - 更柔和
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --text-muted: #a0aec0;
  --text-accent: #4a9eff;

  // 边框和分割线 - 降低对比度
  --border-color: rgba(74, 158, 255, 0.12);
  --border-hover: rgba(74, 158, 255, 0.25);
  --divider: rgba(255, 255, 255, 0.06);

  // 阴影 - 更柔和的阴影
  --shadow-sm: 0 2px 8px rgba(74, 158, 255, 0.06);
  --shadow-md: 0 4px 16px rgba(74, 158, 255, 0.08);
  --shadow-lg: 0 8px 32px rgba(74, 158, 255, 0.1);
  --shadow-glow: 0 0 16px rgba(74, 158, 255, 0.15);

  // 渐变 - 更温和的渐变
  --gradient-primary: linear-gradient(135deg, #4a9eff 0%, #2d7dd2 100%);
  --gradient-secondary: linear-gradient(135deg, #242933 0%, #2d3748 100%);
  --gradient-card: linear-gradient(135deg, rgba(36, 41, 51, 0.9) 0%, rgba(45, 55, 72, 0.9) 100%);
}

// 全局样式重写
* {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--bg-secondary);
}

*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

*::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

*::-webkit-scrollbar-thumb:hover {
  background: var(--primary-light);
}

// 玻璃态效果
.glass-effect {
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
}

// 柔和发光效果
.glow-effect {
  box-shadow: var(--shadow-glow);
  transition: all 0.4s ease;

  &:hover {
    box-shadow: 0 0 20px rgba(74, 158, 255, 0.2);
    transform: translateY(-1px);
  }
}

// 温和科技感按钮
.tech-button {
  background: var(--gradient-primary);
  border: none;
  border-radius: 8px;
  color: var(--text-primary);
  padding: 12px 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.6s ease;
  }

  &:hover::before {
    left: 100%;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  &:active {
    transform: translateY(0);
  }
}

// 科技感卡片
.tech-card {
  background: var(--gradient-card);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  
  &:hover {
    border-color: var(--border-hover);
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
  }
}

// 科技感输入框
.tech-input {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  padding: 12px 16px;
  transition: all 0.3s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
    background: rgba(255, 255, 255, 0.08);
  }
  
  &::placeholder {
    color: var(--text-muted);
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes slideInFromLeft {
  0% {
    transform: translateX(-100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeInUp {
  0% {
    transform: translateY(30px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

// 科技感表格
.tech-table {
  background: var(--bg-card);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: var(--shadow-md);
  
  th {
    background: var(--gradient-secondary);
    color: var(--text-primary);
    font-weight: 600;
    padding: 16px;
    border-bottom: 1px solid var(--border-color);
  }
  
  td {
    padding: 16px;
    border-bottom: 1px solid var(--divider);
    color: var(--text-secondary);
  }
  
  tr:hover {
    background: rgba(0, 212, 255, 0.05);
  }
}

// 科技感导航
.tech-nav {
  background: var(--gradient-card);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-color);
  
  .nav-item {
    color: var(--text-secondary);
    transition: all 0.3s ease;
    
    &:hover {
      color: var(--primary-color);
      text-shadow: 0 0 8px rgba(0, 212, 255, 0.5);
    }
    
    &.active {
      color: var(--primary-color);
      position: relative;
      
      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background: var(--gradient-primary);
      }
    }
  }
}

// 科技感侧边栏
.tech-sidebar {
  background: var(--gradient-secondary);
  border-right: 1px solid var(--border-color);
  
  .menu-item {
    color: var(--text-secondary);
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 4px 8px;
    
    &:hover {
      background: rgba(0, 212, 255, 0.1);
      color: var(--primary-color);
      transform: translateX(4px);
    }
    
    &.active {
      background: var(--gradient-primary);
      color: var(--text-primary);
      box-shadow: var(--shadow-glow);
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .tech-card {
    padding: 16px;
    border-radius: 12px;
  }
  
  .tech-button {
    padding: 10px 20px;
    font-size: 14px;
  }
}
