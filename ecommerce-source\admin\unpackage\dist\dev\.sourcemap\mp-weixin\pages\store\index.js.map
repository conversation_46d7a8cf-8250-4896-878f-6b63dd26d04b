{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?617b", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?4052", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?7841", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?9d88", "uni-app:///pages/store/index.vue", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?5be2", "webpack:///C:/Users/<USER>/Desktop/解析包/ecommerce-source/admin/pages/store/index.vue?b14f"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "searchKeyword", "currentTab", "loading", "productList", "statusTabs", "label", "value", "onLoad", "onReachBottom", "methods", "loadProductList", "setTimeout", "id", "storeName", "price", "otPrice", "stock", "sales", "isShow", "image", "console", "uni", "title", "icon", "switchTab", "handleSearch", "loadMore", "viewProductDetail", "url", "toggleProductStatus", "product", "addProduct"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACuO;AACvO,gBAAgB,qOAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAy6B,CAAgB,u4BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC8E77B;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC,aACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;EACA;EAEAC;IACA;EACA;EAEAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;kBACA;kBACAC;oBACA,qBACA;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAP;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,GACA;sBACAP;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;oBACA,EACA;oBACA;kBACA;gBACA;kBACAC;kBACAC;oBACAC;oBACAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAC;MACA;MACA;IACA;IAEAC;MACA;IACA;IAEAC;MACA;IAAA,CACA;IAEAC;MACAN;QACAO;MACA;IACA;IAEAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;kBACAC;kBACAT;oBACAC;oBACAC;kBACA;gBACA;kBACAH;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEAQ;MACAV;QACAO;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClMA;AAAA;AAAA;AAAA;AAAovD,CAAgB,8mDAAG,EAAC,C;;;;;;;;;;;ACAxwD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/store/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/store/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=38de483b&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=38de483b&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"38de483b\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/store/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=38de483b&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.loading && _vm.productList.length === 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"store-page\">\n    <view class=\"search-bar\">\n      <input \n        v-model=\"searchKeyword\" \n        placeholder=\"搜索商品名称\" \n        class=\"search-input\"\n        @confirm=\"handleSearch\"\n      />\n      <button class=\"search-btn\" @click=\"handleSearch\">搜索</button>\n    </view>\n    \n    <view class=\"filter-tabs\">\n      <view \n        class=\"tab-item\" \n        :class=\"{ active: currentTab === item.value }\"\n        v-for=\"item in statusTabs\" \n        :key=\"item.value\"\n        @click=\"switchTab(item.value)\"\n      >\n        {{ item.label }}\n      </view>\n    </view>\n    \n    <view class=\"product-list\">\n      <view \n        class=\"product-item\" \n        v-for=\"(product, index) in productList\" \n        :key=\"index\"\n        @click=\"viewProductDetail(product)\"\n      >\n        <view class=\"product-image\">\n          <image :src=\"product.image || '/static/default-product.png'\" mode=\"aspectFill\"></image>\n        </view>\n        \n        <view class=\"product-info\">\n          <view class=\"product-name\">{{ product.storeName }}</view>\n          <view class=\"product-price\">\n            <text class=\"current-price\">¥{{ product.price }}</text>\n            <text class=\"original-price\" v-if=\"product.otPrice > product.price\">¥{{ product.otPrice }}</text>\n          </view>\n          <view class=\"product-stats\">\n            <text class=\"stock\">库存：{{ product.stock }}</text>\n            <text class=\"sales\">销量：{{ product.sales || 0 }}</text>\n          </view>\n        </view>\n        \n        <view class=\"product-status\">\n          <view class=\"status-badge\" :class=\"product.isShow ? 'status-online' : 'status-offline'\">\n            {{ product.isShow ? '上架' : '下架' }}\n          </view>\n          <view class=\"product-actions\">\n            <button \n              class=\"action-btn\" \n              @click.stop=\"toggleProductStatus(product)\"\n            >\n              {{ product.isShow ? '下架' : '上架' }}\n            </button>\n          </view>\n        </view>\n      </view>\n    </view>\n    \n    <view class=\"add-product-btn\" @click=\"addProduct\">\n      <text>+</text>\n    </view>\n    \n    <view v-if=\"loading\" class=\"loading\">\n      <text>加载中...</text>\n    </view>\n    \n    <view v-if=\"!loading && productList.length === 0\" class=\"empty\">\n      <text>暂无商品数据</text>\n    </view>\n  </view>\n</template>\n\n<script>\nexport default {\n  data() {\n    return {\n      searchKeyword: '',\n      currentTab: 'all',\n      loading: false,\n      productList: [],\n      statusTabs: [\n        { label: '全部', value: 'all' },\n        { label: '上架中', value: 'online' },\n        { label: '已下架', value: 'offline' },\n        { label: '库存不足', value: 'lowstock' }\n      ]\n    }\n  },\n  \n  onLoad() {\n    this.loadProductList()\n  },\n  \n  onReachBottom() {\n    this.loadMore()\n  },\n  \n  methods: {\n    async loadProductList() {\n      this.loading = true\n      try {\n        // 模拟商品列表数据\n        setTimeout(() => {\n          this.productList = [\n            {\n              id: 1,\n              storeName: 'iPhone 15 Pro Max',\n              price: '9999.00',\n              otPrice: '10999.00',\n              stock: 50,\n              sales: 128,\n              isShow: true,\n              image: '/static/default-product.png'\n            },\n            {\n              id: 2,\n              storeName: 'MacBook Pro 16英寸',\n              price: '19999.00',\n              otPrice: '21999.00',\n              stock: 25,\n              sales: 45,\n              isShow: true,\n              image: '/static/default-product.png'\n            },\n            {\n              id: 3,\n              storeName: 'AirPods Pro 2',\n              price: '1899.00',\n              otPrice: '1999.00',\n              stock: 0,\n              sales: 256,\n              isShow: false,\n              image: '/static/default-product.png'\n            }\n          ]\n          this.loading = false\n        }, 1000)\n      } catch (error) {\n        console.error('加载商品失败:', error)\n        uni.showToast({\n          title: '加载失败',\n          icon: 'none'\n        })\n        this.loading = false\n      }\n    },\n    \n    switchTab(tab) {\n      this.currentTab = tab\n      this.loadProductList()\n    },\n    \n    handleSearch() {\n      this.loadProductList()\n    },\n    \n    loadMore() {\n      // 实现分页加载\n    },\n    \n    viewProductDetail(product) {\n      uni.navigateTo({\n        url: `/pages/store/detail?id=${product.id}`\n      })\n    },\n    \n    async toggleProductStatus(product) {\n      try {\n        // 模拟状态切换\n        product.isShow = !product.isShow\n        uni.showToast({\n          title: product.isShow ? '商品已上架' : '商品已下架',\n          icon: 'success'\n        })\n      } catch (error) {\n        console.error('更新商品状态失败:', error)\n        uni.showToast({\n          title: '操作失败',\n          icon: 'none'\n        })\n      }\n    },\n    \n    addProduct() {\n      uni.navigateTo({\n        url: '/pages/store/add'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.store-page {\n  background: $crmeb-bg-color;\n  min-height: 100vh;\n  padding-bottom: 120rpx;\n}\n\n.search-bar {\n  display: flex;\n  padding: 20rpx;\n  background: white;\n  \n  .search-input {\n    flex: 1;\n    height: 70rpx;\n    border: 2rpx solid #e4e7ed;\n    border-radius: 8rpx;\n    padding: 0 20rpx;\n    margin-right: 20rpx;\n  }\n  \n  .search-btn {\n    width: 120rpx;\n    height: 70rpx;\n    background: $crmeb-color-primary;\n    color: white;\n    border: none;\n    border-radius: 8rpx;\n    font-size: 28rpx;\n  }\n}\n\n.filter-tabs {\n  display: flex;\n  background: white;\n  border-top: 1rpx solid #f0f0f0;\n  \n  .tab-item {\n    flex: 1;\n    text-align: center;\n    padding: 30rpx 0;\n    font-size: 28rpx;\n    color: #666;\n    border-bottom: 4rpx solid transparent;\n    \n    &.active {\n      color: $crmeb-color-primary;\n      border-bottom-color: $crmeb-color-primary;\n    }\n  }\n}\n\n.product-list {\n  padding: 20rpx;\n}\n\n.product-item {\n  display: flex;\n  background: white;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 20rpx;\n  \n  .product-image {\n    width: 160rpx;\n    height: 160rpx;\n    border-radius: 12rpx;\n    overflow: hidden;\n    margin-right: 30rpx;\n    \n    image {\n      width: 100%;\n      height: 100%;\n    }\n  }\n  \n  .product-info {\n    flex: 1;\n    \n    .product-name {\n      font-size: 32rpx;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 20rpx;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n    \n    .product-price {\n      margin-bottom: 20rpx;\n      \n      .current-price {\n        font-size: 36rpx;\n        font-weight: bold;\n        color: $crmeb-color-primary;\n        margin-right: 20rpx;\n      }\n      \n      .original-price {\n        font-size: 24rpx;\n        color: #999;\n        text-decoration: line-through;\n      }\n    }\n    \n    .product-stats {\n      .stock, .sales {\n        font-size: 24rpx;\n        color: #666;\n        margin-right: 30rpx;\n      }\n    }\n  }\n  \n  .product-status {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    \n    .status-badge {\n      padding: 10rpx 20rpx;\n      border-radius: 20rpx;\n      font-size: 24rpx;\n      margin-bottom: 20rpx;\n      \n      &.status-online {\n        background: rgba(25, 190, 107, 0.1);\n        color: $crmeb-color-success;\n      }\n      \n      &.status-offline {\n        background: rgba(237, 64, 20, 0.1);\n        color: $crmeb-color-error;\n      }\n    }\n    \n    .action-btn {\n      width: 100rpx;\n      height: 60rpx;\n      background: $crmeb-color-primary;\n      color: white;\n      border: none;\n      border-radius: 8rpx;\n      font-size: 24rpx;\n    }\n  }\n}\n\n.add-product-btn {\n  position: fixed;\n  bottom: 100rpx;\n  right: 40rpx;\n  width: 100rpx;\n  height: 100rpx;\n  background: $crmeb-color-primary;\n  border-radius: 50%;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow: 0 8rpx 20rpx rgba(233, 51, 35, 0.3);\n  \n  text {\n    font-size: 60rpx;\n    color: white;\n    font-weight: bold;\n  }\n}\n\n.loading, .empty {\n  text-align: center;\n  padding: 80rpx;\n  color: #999;\n}\n</style>\n", "import mod from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=38de483b&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\1000phone\\\\基础屏幕录制\\\\source\\\\helloworld\\\\HBuilderX.4.29.2024093009\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=38de483b&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753685164108\n      var cssReload = require(\"D:/1000phone/基础屏幕录制/source/helloworld/HBuilderX.4.29.2024093009/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}