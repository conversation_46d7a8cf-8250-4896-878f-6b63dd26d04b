{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:31.934",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:32.007",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:32.207",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:32.208",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@43479e68]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:32.211",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.375",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.377",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.377",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.378",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.378",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.386",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.404",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.406",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1d1101c8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.407",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.429",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.430",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2981af08]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.433",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.448",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.448",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2e10128b]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.450",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:12:56.450",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:00.358",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:00.360",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:00.487",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:00.488",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@41e07d60]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:00.492",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.889",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/image/domain", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getImageDomain()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.898",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.898",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3a97a705]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.899",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:53.998",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/detail/92?type=normal", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.000",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/product/92", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.001",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?productId=92", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.002",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.002",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductReply(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.002",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/config/92", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.002",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getDetail(Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.004",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getReplyCount(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.005",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/good", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.005",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=999&productId=92&type=1", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.006",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getGoodProductList()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.007",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.047",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.049",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@43b55c6c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.056",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-29",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.067",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.068",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@677ac252]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.071",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-26",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.086",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.087",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@29a4478a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.089",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.138",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.138",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.139",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@47d003b8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.139",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@69f32a08]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.140",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-30",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.141",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-27",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.558",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.558",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2427346a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.570",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.587",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/qrcode/get", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.589",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.QrCodeController#get(JSONObject)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.598",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [{"path":"pages/goods/goods_details/index","id":"92"}]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.636",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=111111&secret=111111" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:54.637",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.042",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.043",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.055",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.093",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.094",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@44bab37]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.094",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid rid: 6887a23a-112fabfe-31abf01d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:15:55.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.754",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/image/domain", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.755",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getImageDomain()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.763",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.763",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3ac8ee28]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.764",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.771",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/detail/92?type=normal", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.773",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getDetail(Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.774",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?productId=92", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.775",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.776",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/product/92", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.778",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductReply(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.779",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/config/92", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.782",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getReplyCount(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.782",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/good", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.784",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getGoodProductList()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.788",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.789",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7b51adfd]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.791",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.795",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=999&productId=92&type=1", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.796",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.813",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.814",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4452bc9c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.816",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.818",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.820",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@636ce3f5]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.822",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-1",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.829",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.830",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6f0317b1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.831",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.844",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.845",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7539bc03]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:18.847",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.029",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.030",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@22b5fcf1]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.039",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.060",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/qrcode/get", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.061",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.QrCodeController#get(JSONObject)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.063",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [{"path":"pages/goods/goods_details/index","id":"92"}]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.095",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=111111&secret=111111" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.096",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.449",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.451",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.461",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.494",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.494",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7a794f8c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.495",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid rid: 6887a252-0be6f9a4-0386a931]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:16:19.495",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.923",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/image/domain", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.924",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getImageDomain()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.937",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.938",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6443cbc4]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.939",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-18",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.940",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/detail/91?type=normal", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.941",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getDetail(Integer, String)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.942",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?productId=91", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.943",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.944",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/product/91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.945",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getProductReply(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.949",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/reply/config/91", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.951",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getReplyCount(Integer)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.955",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/good", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.956",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getGoodProductList()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.976",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.978",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/coupons?page=1&limit=999&productId=91&type=1", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.978",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7564d983]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.980",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CouponController#getList(int, int, PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.980",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.985",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.986",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@613997a8]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.987",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.996",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.997",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@694b180a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:04.998",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.005",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.006",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5f9345fb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.008",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.024",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.025",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@47874642]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.027",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.133",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.134",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6caae89d]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.137",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.154",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/qrcode/get", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.155",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.QrCodeController#get(JSONObject)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.157",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Read "application/json;charset=UTF-8" to [{"path":"pages/goods/goods_details/index","id":"91"}]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.186",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "HTTP GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=111111&secret=111111" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.186",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Accept=[application/json, application/cbor, application/*+json, text/plain, text/html]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.577",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Response 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.578",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.client.RestTemplate",
                    "message": "Reading to [com.alibaba.fastjson.JSONObject]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.625",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.625",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3de085b2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.626",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [com.zbkj.common.exception.CrmebException: 微信接口调用失败：40013invalid appid rid: 6887a335-252bc997-58473a22]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:20:05.627",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.858",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.859",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.861",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.861",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.862",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.862",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.880",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.889",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.889",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7b19765c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.890",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.911",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.912",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@1ee8cf47]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.914",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.935",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.937",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@52e53862]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.938",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:21:19.940",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:42:29.961",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:42:29.967",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:42:30.088",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:42:30.089",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@41e0488e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:42:30.092",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:25.706",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:25.708",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:25.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:25.891",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5d6e7c23]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:25.893",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.669",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.670",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.671",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.670",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.671",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.672",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.681",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.683",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.683",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@201c3600]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.685",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-9",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.729",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.730",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6b176c0c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.733",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-6",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.770",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.771",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@6bfd38af]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.772",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:44:31.772",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-10",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.636",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "POST "/api/front/wechat/authorize/program/login", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.637",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#programLogin(String, RegisterThirdUserRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.639",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.ServletInvocableHandlerMethod",
                    "message": "Could not resolve parameter [0] in public com.zbkj.common.result.CommonResult<com.zbkj.common.response.LoginResponse> com.zbkj.front.controller.WeChatController.programLogin(java.lang.String,com.zbkj.common.request.RegisterThirdUserRequest): Required String parameter 'code' is not present" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.639",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.664",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.665",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@52d3b250]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.665",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.web.bind.MissingServletRequestParameterException: Required String parameter 'code' is not present]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:47:14.665",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-20",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:19.790",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:19.803",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:19.899",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:19.899",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@309b799a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:19.902",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-22",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.712",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.713",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.717",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.719",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.721",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.722",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.725",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.736",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.736",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@45a05477]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.738",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-12",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.744",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.745",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@24470168]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.746",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-21",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.777",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.778",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2447b942]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.779",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:51:22.780",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-11",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.379",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.385",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.390",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.391",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@5089586a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.394",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-14",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.425",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3a0b4dfe]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-19",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@7e00fed2]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:52:17.426",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-23",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:02.096",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:02.097",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:02.170",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:02.170",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@68dd2bbd]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:02.170",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-16",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:12.112",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:12.113",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:12.182",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:12.182",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@4f19b159]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:54:12.183",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-25",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.652",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/index", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.652",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getIndexInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.713",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.713",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@76841f1a]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.713",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-28",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.734",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.734",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.803",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.803",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@29c73d1e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.803",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-17",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.817",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/category", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.817",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getCategory()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@2c53f80f]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.837",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-24",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.852",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/wechat/getLogo", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.853",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.WeChatController#getLogo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.858",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.860",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@20449d96]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:56:28.861",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-4",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:33.588",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/product/hot?page=1&limit=10", parameters={masked}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:33.597",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.ProductController#getHotProductList(PageParamRequest)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:33.700",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:33.701",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@33782bdb]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:33.701",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-8",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.720",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/menu/user", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.720",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.UserController#getMenuUser()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.726",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/copyright/info", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.727",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.IndexController#getCopyrightInfo()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.727",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "GET "/api/front/city/list", parameters={}" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.728",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestMappingHandlerMapping",
                    "message": "Mapped to com.zbkj.front.controller.CityController#register()" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.744",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.745",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@e80e6ef]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.746",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-3",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.751",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Using @ExceptionHandler com.zbkj.service.exception.GlobalExceptionHandler#defaultExceptionHandler(HttpServletRequest, Exception)" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.752",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.754",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@3cdb250e]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.754",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-7",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.792",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json, application/cbor]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.793",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.RequestResponseBodyMethodProcessor",
                    "message": "Writing [com.zbkj.common.result.CommonResult@24c42e4c]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.794",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver",
                    "message": "Resolved [org.springframework.data.redis.RedisConnectionFailureException: Cannot get Jedis connection; nested exception is redis.clients.jedis.exceptions.JedisConnectionException: Could not get a resource from the pool]" }
                    
{
                    "app": "Crmeb",
                    "timestamp":"2025-07-29 00:58:36.794",
                    "level": "DEBUG",
                    "thread": "http-nio-8080-exec-2",
                    "class": "o.s.web.servlet.DispatcherServlet",
                    "message": "Completed 200 OK" }
                    
