<view class="container data-v-57280228"><view class="header data-v-57280228"><text class="title data-v-57280228">🎉 CRMEB 小程序管理后台</text><text class="subtitle data-v-57280228">项目配置成功！</text></view><view class="content data-v-57280228"><view class="card data-v-57280228"><text class="card-title data-v-57280228">✅ 编译状态</text><text class="card-desc data-v-57280228">项目已成功编译，可以正常运行</text></view><view class="card data-v-57280228"><text class="card-title data-v-57280228">🔧 配置信息</text><text class="card-desc data-v-57280228">{{"API地址："+apiUrl}}</text><text class="card-desc data-v-57280228">{{"版本："+version}}</text></view><view class="card data-v-57280228"><text class="card-title data-v-57280228">📱 系统信息</text><text class="card-desc data-v-57280228">{{"平台："+systemInfo.platform}}</text><text class="card-desc data-v-57280228">{{"版本："+systemInfo.version}}</text></view></view><view class="navigation data-v-57280228"><text class="nav-title data-v-57280228">🚀 管理功能</text><view class="nav-grid data-v-57280228"><view data-event-opts="{{[['tap',[['goToLogin',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">🔐</text><text class="nav-text data-v-57280228">登录</text></view><view data-event-opts="{{[['tap',[['goToDashboard',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">📊</text><text class="nav-text data-v-57280228">控制台</text></view><view data-event-opts="{{[['tap',[['goToOrder',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">📦</text><text class="nav-text data-v-57280228">订单管理</text></view><view data-event-opts="{{[['tap',[['goToUser',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">👥</text><text class="nav-text data-v-57280228">用户管理</text></view><view data-event-opts="{{[['tap',[['goToStore',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">🛍️</text><text class="nav-text data-v-57280228">商品管理</text></view><view data-event-opts="{{[['tap',[['testApi',['$event']]]]]}}" class="nav-item data-v-57280228" bindtap="__e"><text class="nav-icon data-v-57280228">🔧</text><text class="nav-text data-v-57280228">测试API</text></view></view></view></view>