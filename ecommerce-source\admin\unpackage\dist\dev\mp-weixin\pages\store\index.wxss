@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
/* 颜色变量 */
/* 行为相关颜色 */
/* 文字基本颜色 */
/* 背景颜色 */
/* 边框颜色 */
/* 尺寸变量 */
/* 文字尺寸 */
/* 图片尺寸 */
/* Border Radius */
/* 水平间距 */
/* 垂直间距 */
/* 透明度 */
/* 文章场景相关 */
/* CRMEB 自定义主题色 */
.store-page.data-v-38de483b {
  background: #f8f8f9;
  min-height: 100vh;
  padding-bottom: 120rpx;
}
.search-bar.data-v-38de483b {
  display: flex;
  padding: 20rpx;
  background: white;
}
.search-bar .search-input.data-v-38de483b {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e4e7ed;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-right: 20rpx;
}
.search-bar .search-btn.data-v-38de483b {
  width: 120rpx;
  height: 70rpx;
  background: #E93323;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 28rpx;
}
.filter-tabs.data-v-38de483b {
  display: flex;
  background: white;
  border-top: 1rpx solid #f0f0f0;
}
.filter-tabs .tab-item.data-v-38de483b {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  border-bottom: 4rpx solid transparent;
}
.filter-tabs .tab-item.active.data-v-38de483b {
  color: #E93323;
  border-bottom-color: #E93323;
}
.product-list.data-v-38de483b {
  padding: 20rpx;
}
.product-item.data-v-38de483b {
  display: flex;
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}
.product-item .product-image.data-v-38de483b {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
  margin-right: 30rpx;
}
.product-item .product-image image.data-v-38de483b {
  width: 100%;
  height: 100%;
}
.product-item .product-info.data-v-38de483b {
  flex: 1;
}
.product-item .product-info .product-name.data-v-38de483b {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.product-item .product-info .product-price.data-v-38de483b {
  margin-bottom: 20rpx;
}
.product-item .product-info .product-price .current-price.data-v-38de483b {
  font-size: 36rpx;
  font-weight: bold;
  color: #E93323;
  margin-right: 20rpx;
}
.product-item .product-info .product-price .original-price.data-v-38de483b {
  font-size: 24rpx;
  color: #999;
  text-decoration: line-through;
}
.product-item .product-info .product-stats .stock.data-v-38de483b, .product-item .product-info .product-stats .sales.data-v-38de483b {
  font-size: 24rpx;
  color: #666;
  margin-right: 30rpx;
}
.product-item .product-status.data-v-38de483b {
  display: flex;
  flex-direction: column;
  align-items: center;
}
.product-item .product-status .status-badge.data-v-38de483b {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  margin-bottom: 20rpx;
}
.product-item .product-status .status-badge.status-online.data-v-38de483b {
  background: rgba(25, 190, 107, 0.1);
  color: #19be6b;
}
.product-item .product-status .status-badge.status-offline.data-v-38de483b {
  background: rgba(237, 64, 20, 0.1);
  color: #ed4014;
}
.product-item .product-status .action-btn.data-v-38de483b {
  width: 100rpx;
  height: 60rpx;
  background: #E93323;
  color: white;
  border: none;
  border-radius: 8rpx;
  font-size: 24rpx;
}
.add-product-btn.data-v-38de483b {
  position: fixed;
  bottom: 100rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background: #E93323;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8rpx 20rpx rgba(233, 51, 35, 0.3);
}
.add-product-btn text.data-v-38de483b {
  font-size: 60rpx;
  color: white;
  font-weight: bold;
}
.loading.data-v-38de483b, .empty.data-v-38de483b {
  text-align: center;
  padding: 80rpx;
  color: #999;
}
